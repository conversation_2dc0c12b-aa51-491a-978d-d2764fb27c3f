-- Create storage bucket for book covers
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'book-covers',
    'book-covers',
    true,
    5242880, -- 5MB limit
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
);

-- Create policy to allow public uploads
CREATE POLICY "Allow public uploads" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'book-covers');

-- Create policy to allow public access
CREATE POLICY "Allow public access" ON storage.objects
    FOR SELECT USING (bucket_id = 'book-covers');

-- Create policy to allow public updates
CREATE POLICY "Allow public updates" ON storage.objects
    FOR UPDATE USING (bucket_id = 'book-covers');

-- Create policy to allow public deletes
CREATE POLICY "Allow public deletes" ON storage.objects
    FOR DELETE USING (bucket_id = 'book-covers');
