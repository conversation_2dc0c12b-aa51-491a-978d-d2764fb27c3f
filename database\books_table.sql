-- Add genre column if it doesn't exist (safe migration)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'books' AND column_name = 'genre') THEN
        ALTER TABLE public.books ADD COLUMN genre text null;
    END IF;
END $$;

-- Create books table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.books (
  id uuid not null default extensions.uuid_generate_v4(),
  title text not null,
  description text null,
  cover_image_url text null,
  author_id uuid null,
  status text not null default 'draft'::text,
  genre text null,
  tags text[] null,
  total_chapters integer null default 0,
  total_words integer null default 0,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint books_pkey primary key (id),
  constraint books_author_id_fkey foreign key (author_id) references users (id) on delete cascade
) tablespace pg_default;

-- Enable Row Level Security (safe to run multiple times)
ALTER TABLE public.books ENABLE ROW LEVEL SECURITY;

-- Create policies for books table (with IF NOT EXISTS equivalent)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow public read access') THEN
        CREATE POLICY "Allow public read access" ON public.books FOR SELECT USING (true);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow authenticated users to insert') THEN
        CREATE POLICY "Allow authenticated users to insert" ON public.books FOR INSERT WITH CHECK (true);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow users to update their own books') THEN
        CREATE POLICY "Allow users to update their own books" ON public.books FOR UPDATE USING (true);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow users to delete their own books') THEN
        CREATE POLICY "Allow users to delete their own books" ON public.books FOR DELETE USING (true);
    END IF;
END $$;
