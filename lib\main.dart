import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'features/auth/presentation/widgets/auth_wrapper.dart';
import 'features/books/presentation/pages/home_page.dart';
import 'features/books/presentation/pages/library_page.dart';
import 'features/books/presentation/widgets/main_bottom_navigation.dart';
import 'features/books/presentation/pages/write/book_details_page.dart';
import 'features/books/presentation/pages/write/my_books_page.dart';
import 'core/injection/injection_container.dart' as di;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: 'https://qvlurdcrckbmzpyvntip.supabase.co',
    anon<PERSON>ey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2bHVyZGNyY2tibXpweXZudGlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MTA0NTksImV4cCI6MjA2NTk4NjQ1OX0.TSzAsqH72wtlrT6AcfN9s__RYjDuIrzaORSCnZRZyxQ',
  );

  // Initialize dependency injection
  await di.init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'WhatTheBook',
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        FlutterQuillLocalizations.delegate,
      ],
      supportedLocales: const [Locale('en', 'US'), Locale('id', 'ID')],
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFB8860B), // Dark golden color
          primary: const Color(0xFFB8860B),
          secondary: const Color(0xFFDAA520),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFFB8860B),
          foregroundColor: Colors.white,
        ),
        floatingActionButtonTheme: const FloatingActionButtonThemeData(
          backgroundColor: Color(0xFFB8860B),
          foregroundColor: Colors.white,
        ),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
      routes: {'/main': (context) => const MainScreen()},
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: const [
          // Home Tab
          HomePage(),
          // Library Tab
          LibraryPage(),
          // Write Tab
          WriteTab(),
          // Profile Tab
          Center(child: Text('Profil')),
        ],
      ),
      bottomNavigationBar: MainBottomNavigation(
        currentIndex: _currentIndex,
        onDestinationSelected: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }
}

class WriteTab extends StatelessWidget {
  const WriteTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const MyBooksPage();
  }
}
