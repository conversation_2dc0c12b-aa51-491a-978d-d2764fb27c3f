import 'package:flutter/material.dart';
import 'editor_block.dart';

class FloatingAddButton extends StatefulWidget {
  final Function(BlockType) onAddBlock;

  const FloatingAddButton({
    super.key,
    required this.onAddBlock,
  });

  @override
  State<FloatingAddButton> createState() => _FloatingAddButtonState();
}

class _FloatingAddButtonState extends State<FloatingAddButton>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.125).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    if (_isExpanded) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  void _addBlock(BlockType type) {
    widget.onAddBlock(type);
    _toggleExpanded();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Background overlay
        if (_isExpanded)
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleExpanded,
              child: Container(
                color: Colors.black.withValues(alpha: 0.3),
              ),
            ),
          ),

        // Block type options
        ..._buildBlockOptions(),

        // Main FAB
        FloatingActionButton(
          heroTag: "add_block",
          onPressed: _toggleExpanded,
          backgroundColor: Theme.of(context).primaryColor,
          child: AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: Icon(
                  _isExpanded ? Icons.close : Icons.add,
                  color: Colors.white,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Widget> _buildBlockOptions() {
    final options = [
      _BlockOption(
        icon: Icons.text_fields,
        label: 'Text',
        type: BlockType.text,
        offset: const Offset(0, -70),
      ),
      _BlockOption(
        icon: Icons.looks_one_outlined,
        label: 'Heading',
        type: BlockType.heading1,
        offset: const Offset(0, -140),
      ),
      _BlockOption(
        icon: Icons.radio_button_unchecked,
        label: 'List',
        type: BlockType.bulletList,
        offset: const Offset(-70, -70),
      ),
      _BlockOption(
        icon: Icons.format_quote_outlined,
        label: 'Quote',
        type: BlockType.quote,
        offset: const Offset(-70, 0),
      ),
      _BlockOption(
        icon: Icons.image_outlined,
        label: 'Image',
        type: BlockType.image,
        offset: const Offset(-140, 0),
      ),
    ];

    return options.map((option) {
      return AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: option.offset * _scaleAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: _scaleAnimation.value,
                child: _buildOptionButton(option),
              ),
            ),
          );
        },
      );
    }).toList();
  }

  Widget _buildOptionButton(_BlockOption option) {
    return Container(
      margin: const EdgeInsets.all(4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.small(
            heroTag: "add_${option.type.toString()}",
            onPressed: () => _addBlock(option.type),
            backgroundColor: Colors.white,
            foregroundColor: Theme.of(context).primaryColor,
            elevation: 4,
            child: Icon(option.icon, size: 20),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.black87,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              option.label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _BlockOption {
  final IconData icon;
  final String label;
  final BlockType type;
  final Offset offset;

  _BlockOption({
    required this.icon,
    required this.label,
    required this.type,
    required this.offset,
  });
}
