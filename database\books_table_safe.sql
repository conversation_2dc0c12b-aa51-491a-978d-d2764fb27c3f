-- Safe migration for books table - NO DATA LOSS
-- This script only adds missing columns and creates policies if they don't exist

-- Create books table if it doesn't exist (safe - won't affect existing table)
CREATE TABLE IF NOT EXISTS public.books (
  id uuid not null default extensions.uuid_generate_v4(),
  title text not null,
  description text null,
  cover_image_url text null,
  author_id uuid null,
  status text not null default 'draft'::text,
  genre text null,
  tags text[] null,
  total_chapters integer null default 0,
  total_words integer null default 0,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint books_pkey primary key (id),
  constraint books_author_id_fkey foreign key (author_id) references users (id) on delete cascade
) tablespace pg_default;

-- Enable Row Level Security (safe to run multiple times)
ALTER TABLE public.books ENABLE ROW LEVEL SECURITY;

-- Create policies ONLY if they don't exist (completely safe - no dropping)
DO $$ 
BEGIN
    -- Only create policies if they don't already exist
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow public read access') THEN
        CREATE POLICY "Allow public read access" ON public.books
            FOR SELECT USING (true);
    END IF;
        
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow authenticated users to insert') THEN
        CREATE POLICY "Allow authenticated users to insert" ON public.books
            FOR INSERT WITH CHECK (true);
    END IF;
        
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow users to update their own books') THEN
        CREATE POLICY "Allow users to update their own books" ON public.books
            FOR UPDATE USING (true);
    END IF;
        
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'books' AND policyname = 'Allow users to delete their own books') THEN
        CREATE POLICY "Allow users to delete their own books" ON public.books
            FOR DELETE USING (true);
    END IF;
END $$;
