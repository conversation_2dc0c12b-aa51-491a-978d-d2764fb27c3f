import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://qvlurdcrckbmzpyvntip.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2bHVyZGNyY2tibXpweXZudGlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MTA0NTksImV4cCI6MjA2NTk4NjQ1OX0.TSzAsqH72wtlrT6AcfN9s__RYjDuIrzaORSCnZRZyxQ',
  );

  final client = Supabase.instance.client;

  try {
    print('Creating user profile for existing auth users...');
    
    // Create user profile for the user ID we saw in the screenshot
    final userId = '9fd14ae7-0e04-4d50-aa9c-8f64fb473318';
    final email = '<EMAIL>';
    final fullName = 'Lutfi AMB';
    
    // Check if user profile already exists
    final existingProfile = await client
        .from('users')
        .select('id')
        .eq('id', userId)
        .maybeSingle();
    
    if (existingProfile != null) {
      print('User profile already exists for $userId');
      return;
    }
    
    // Create user profile
    await client.from('users').insert({
      'id': userId,
      'email': email,
      'full_name': fullName,
      'followers_count': 0,
      'following_count': 0,
      'books_count': 0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
    
    print('Successfully created user profile for $userId');
    print('Email: $email');
    print('Full Name: $fullName');
    
    // Verify the profile was created
    final createdProfile = await client
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
    
    print('Verified profile: $createdProfile');
    
    // Test creating a bookmark for this user
    print('Testing bookmark creation...');
    await client.from('bookmarks').insert({
      'user_id': userId,
      'book_id': 'test-book-id',
    });
    print('Bookmark created successfully!');
    
    // Test creating reading progress for this user
    print('Testing reading progress creation...');
    await client.from('reading_progress').insert({
      'user_id': userId,
      'book_id': 'test-book-id',
      'chapter_id': 'test-chapter-id',
      'progress_percentage': 50.0,
      'last_read_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
    print('Reading progress created successfully!');
    
    print('\nUser profile creation completed successfully!');
    print('The user can now use bookmarks and reading progress features.');
    
  } catch (e) {
    print('Error: $e');
  }
}
