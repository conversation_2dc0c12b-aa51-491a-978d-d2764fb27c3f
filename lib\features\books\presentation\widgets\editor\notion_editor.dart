import 'package:flutter/material.dart';
import 'editor_block.dart';
import 'block_widget.dart';
import 'floating_add_button.dart';

class NotionEditor extends StatefulWidget {
  final List<EditorBlock> initialBlocks;
  final Function(List<EditorBlock>) onChanged;

  const NotionEditor({
    super.key,
    required this.initialBlocks,
    required this.onChanged,
  });

  @override
  State<NotionEditor> createState() => _NotionEditorState();
}

class _NotionEditorState extends State<NotionEditor> with TickerProviderStateMixin {
  late List<EditorBlock> _blocks;
  String? _selectedBlockId;
  bool _hasTextSelection = false;
  late AnimationController _toolbarController;
  late Animation<double> _toolbarAnimation;

  @override
  void initState() {
    super.initState();
    _blocks = widget.initialBlocks.isEmpty 
        ? [EditorBlock.empty(type: BlockType.text)]
        : List.from(widget.initialBlocks);
        
    _toolbarController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _toolbarAnimation = CurvedAnimation(
      parent: _toolbarController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _toolbarController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(NotionEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialBlocks != oldWidget.initialBlocks) {
      _blocks = widget.initialBlocks.isEmpty 
          ? [EditorBlock.empty(type: BlockType.text)]
          : List.from(widget.initialBlocks);
    }
  }

  void _onBlockChanged(EditorBlock block) {
    final index = _blocks.indexWhere((b) => b.id == block.id);
    if (index != -1) {
      _blocks[index] = block;
      widget.onChanged(_blocks);
    }
  }

  void _onBlockDeleted(String blockId) {
    if (_blocks.length > 1) {
      setState(() {
        _blocks.removeWhere((block) => block.id == blockId);
      });
      widget.onChanged(_blocks);
    }
  }

  void _onBlockTypeChanged(String blockId, BlockType newType) {
    final index = _blocks.indexWhere((b) => b.id == blockId);
    if (index != -1) {
      setState(() {
        _blocks[index] = EditorBlock(
          id: blockId,
          type: newType,
          content: _blocks[index].content,
          attributes: _blocks[index].attributes,
        );
      });
      widget.onChanged(_blocks);
    }
  }

  void _onNewBlockAfter(String blockId) {
    final index = _blocks.indexWhere((b) => b.id == blockId);
    if (index != -1) {
      setState(() {
        _blocks.insert(
          index + 1,
          EditorBlock.empty(type: BlockType.text),
        );
      });
      widget.onChanged(_blocks);
    }
  }

  void _onFocusChanged(String blockId, bool hasFocus, bool hasSelection) {
    setState(() {
      _hasTextSelection = hasSelection;
      if (hasSelection) {
        _toolbarController.forward();
      } else {
        _toolbarController.reverse();
      }
    });
  }

  void _onAddBlock(BlockType type) {
    setState(() {
      _blocks.add(EditorBlock.empty(type: type));
    });
    widget.onChanged(_blocks);
  }

  void _formatSelectedText(String format) {
    // This would implement text formatting for the selected block
    // For now, we'll just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$format formatting applied')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Editor content
        Container(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
          child: ListView.builder(
            itemCount: _blocks.length,
            itemBuilder: (context, index) {
              final block = _blocks[index];
              return BlockWidget(
                key: ValueKey(block.id),
                block: block,
                isSelected: _selectedBlockId == block.id,
                onChanged: _onBlockChanged,
                onDelete: _onBlockDeleted,
                onTypeChanged: _onBlockTypeChanged,
                onNewBlockAfter: _onNewBlockAfter,
                onFocusChanged: _onFocusChanged,
                onFocusNext: index < _blocks.length - 1 
                    ? () => setState(() => _selectedBlockId = _blocks[index + 1].id)
                    : null,
                onFocusPrevious: index > 0 
                    ? () => setState(() => _selectedBlockId = _blocks[index - 1].id)
                    : null,
              );
            },
          ),
        ),

        // Floating formatting toolbar
        if (_hasTextSelection)
          Positioned(
            top: 16,
            left: 0,
            right: 0,
            child: Center(
              child: FadeTransition(
                opacity: _toolbarAnimation,
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildFormatButton(
                          icon: Icons.format_bold,
                          tooltip: 'Bold',
                          onPressed: () => _formatSelectedText('bold'),
                        ),
                        _buildFormatButton(
                          icon: Icons.format_italic,
                          tooltip: 'Italic',
                          onPressed: () => _formatSelectedText('italic'),
                        ),
                        _buildFormatButton(
                          icon: Icons.format_underlined,
                          tooltip: 'Underline',
                          onPressed: () => _formatSelectedText('underline'),
                        ),
                        const VerticalDivider(width: 16),
                        _buildFormatButton(
                          icon: Icons.link,
                          tooltip: 'Add link',
                          onPressed: () => _formatSelectedText('link'),
                        ),
                        _buildFormatButton(
                          icon: Icons.format_quote,
                          tooltip: 'Quote',
                          onPressed: () => _formatSelectedText('quote'),
                        ),
                        _buildFormatButton(
                          icon: Icons.code,
                          tooltip: 'Code',
                          onPressed: () => _formatSelectedText('code'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

        // Floating add button
        Positioned(
          right: 16,
          bottom: 16,
          child: FloatingAddButton(onAddBlock: _onAddBlock),
        ),
      ],
    );
  }

  Widget _buildFormatButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(4),
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Icon(
              icon,
              size: 18,
              color: Colors.grey.shade700,
            ),
          ),
        ),
      ),
    );
  }
}
