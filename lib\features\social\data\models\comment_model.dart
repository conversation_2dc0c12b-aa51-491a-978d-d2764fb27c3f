import '../../../auth/data/models/user_model.dart';
import '../../domain/entities/comment.dart';

class CommentModel extends Comment {
  const CommentModel({
    required super.id,
    required super.userId,
    required super.bookId,
    super.chapterId,
    required super.content,
    super.parentCommentId,
    required super.createdAt,
    required super.updatedAt,
    super.user,
    super.replies,
  });

  factory CommentModel.fromSupabase(Map<String, dynamic> map) {
    return CommentModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      bookId: map['book_id'] as String,
      chapterId: map['chapter_id'] as String?,
      content: map['content'] as String,
      parentCommentId: map['parent_comment_id'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      user: map['users'] != null 
          ? UserModel.fromSupabase(map['users'] as Map<String, dynamic>).toEntity()
          : null,
      replies: map['replies'] != null
          ? (map['replies'] as List)
              .map((reply) => CommentModel.fromSupabase(reply))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toSupabase() {
    return {
      'id': id,
      'user_id': userId,
      'book_id': bookId,
      'chapter_id': chapterId,
      'content': content,
      'parent_comment_id': parentCommentId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory CommentModel.fromEntity(Comment comment) {
    return CommentModel(
      id: comment.id,
      userId: comment.userId,
      bookId: comment.bookId,
      chapterId: comment.chapterId,
      content: comment.content,
      parentCommentId: comment.parentCommentId,
      createdAt: comment.createdAt,
      updatedAt: comment.updatedAt,
      user: comment.user,
      replies: comment.replies,
    );
  }

  Comment toEntity() {
    return Comment(
      id: id,
      userId: userId,
      bookId: bookId,
      chapterId: chapterId,
      content: content,
      parentCommentId: parentCommentId,
      createdAt: createdAt,
      updatedAt: updatedAt,
      user: user,
      replies: replies,
    );
  }
}
