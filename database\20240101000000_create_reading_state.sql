-- Create reading_state table
CREATE TABLE IF NOT EXISTS public.reading_state (
    id TEXT PRIMARY KEY,
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    bookmarks TEXT[] DEFAULT '{}',
    last_read_chapter UUID REFERENCES chapters(id) ON DELETE SET NULL,
    last_read_page INTEGER DEFAULT 0,
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add RLS policies
ALTER TABLE public.reading_state ENABLE ROW LEVEL SECURITY;

-- Allow read access to authenticated users
CREATE POLICY "Allow read access to authenticated users"
ON public.reading_state
FOR SELECT
TO authenticated
USING (true);

-- Allow insert/update access to authenticated users
CREATE POLICY "Allow insert/update access to authenticated users"
ON public.reading_state
FOR ALL 
TO authenticated
USING (true)
WITH CHECK (true);

-- <PERSON><PERSON> function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_reading_state_updated_at
    BEFORE UPDATE ON public.reading_state
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
