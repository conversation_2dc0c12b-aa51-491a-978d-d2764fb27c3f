import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'book_reader_page.dart';

class BookDetailPage extends StatefulWidget {
  final String bookId;
  final String? title;
  final String? authorName;
  final String? coverImageUrl;

  const BookDetailPage({
    super.key,
    required this.bookId,
    this.title,
    this.authorName,
    this.coverImageUrl,
  });

  @override
  State<BookDetailPage> createState() => _BookDetailPageState();
}

class _BookDetailPageState extends State<BookDetailPage> {
  Map<String, dynamic>? bookData;
  List<Map<String, dynamic>> chapters = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBookDetails();
  }

  Future<void> _loadBookDetails() async {
    try {
      // Load book details
      final bookResponse = await Supabase.instance.client
          .from('books')
          .select()
          .eq('id', widget.bookId)
          .single();

      // Load chapters
      final chaptersResponse = await Supabase.instance.client
          .from('chapters')
          .select()
          .eq('book_id', widget.bookId)
          .order('chapter_number');

      setState(() {
        bookData = bookResponse;
        chapters = List<Map<String, dynamic>>.from(chaptersResponse);
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading book details: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (bookData == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Book Not Found')),
        body: const Center(
          child: Text('Book details could not be loaded.'),
        ),
      );
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with book cover
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                bookData!['title'] ?? 'Untitled',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 3,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
              background: Stack(
                fit: StackFit.expand,
                children: [
                  if (bookData!['cover_image_url'] != null && bookData!['cover_image_url'].isNotEmpty)
                    Image.network(
                      bookData!['cover_image_url'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                        child: const Icon(Icons.book, size: 64, color: Colors.white),
                      ),
                    )
                  else
                    Container(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      child: const Icon(Icons.book, size: 64, color: Colors.white),
                    ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Book Information
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Author and basic info
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'by ${bookData!['author_name'] ?? 'Anonymous'}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Stats
                  Row(
                    children: [
                      _buildStatChip(
                        icon: Icons.remove_red_eye,
                        label: '${bookData!['views'] ?? 0} views',
                      ),
                      const SizedBox(width: 8),
                      _buildStatChip(
                        icon: Icons.favorite,
                        label: '${bookData!['likes'] ?? 0} likes',
                      ),
                      const SizedBox(width: 8),
                      _buildStatChip(
                        icon: Icons.menu_book,
                        label: '${chapters.length} chapters',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Genres
                  if (bookData!['genres'] != null && (bookData!['genres'] as List).isNotEmpty)
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: (bookData!['genres'] as List<dynamic>)
                          .map((genre) => Chip(
                                label: Text(genre.toString()),
                                backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              ))
                          .toList(),
                    ),
                  const SizedBox(height: 16),

                  // Description
                  Text(
                    'Description',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    bookData!['description'] ?? 'No description available.',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 24),

                  // Chapters section
                  Text(
                    'Chapters',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),

          // Chapters list
          if (chapters.isEmpty)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Center(
                  child: Text('No chapters available yet.'),
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final chapter = chapters[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: ListTile(
                      leading: CircleAvatar(
                        child: Text('${index + 1}'),
                      ),
                      title: Text(chapter['title'] ?? 'Chapter ${index + 1}'),
                      subtitle: Text('${chapter['word_count'] ?? 0} words'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        // Navigate to book reader at specific chapter
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BookReaderPage(
                              bookId: widget.bookId,
                              initialChapterId: chapter['id'],
                              bookTitle: bookData!['title'] ?? 'Untitled',
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
                childCount: chapters.length,
              ),
            ),

          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Navigate to book reader starting from first chapter
          if (chapters.isNotEmpty) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => BookReaderPage(
                  bookId: widget.bookId,
                  initialChapterId: chapters.first['id'],
                  bookTitle: bookData!['title'] ?? 'Untitled',
                ),
              ),
            );
          }
        },
        label: const Text('Start Reading'),
        icon: const Icon(Icons.play_arrow),
      ),
    );
  }

  Widget _buildStatChip({required IconData icon, required String label}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

