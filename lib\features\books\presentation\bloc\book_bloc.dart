import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/usecases/create_book.dart';
import '../../domain/usecases/get_book_detail.dart';
import '../../domain/usecases/get_books.dart';
import 'book_event.dart';
import 'book_state.dart';

class BookBloc extends Bloc<BookEvent, BookState> {
  final GetBooks getBooks;
  final GetBookDetail getBookDetail;
  final CreateBook createBook;

  BookBloc({
    required this.getBooks,
    required this.getBookDetail,
    required this.createBook,
  }) : super(BookInitial()) {
    on<GetBooksEvent>(_onGetBooks);
    on<GetBookDetailEvent>(_onGetBookDetail);
    on<CreateBookEvent>(_onCreateBook);
    on<RefreshBooksEvent>(_onRefreshBooks);
  }

  Future<void> _onGetBooks(GetBooksEvent event, Emitter<BookState> emit) async {
    if (event.page == 1) {
      emit(BookLoading());
    } else if (state is BookLoaded) {
      emit(BookLoadingMore(
        books: (state as BookLoaded).books,
        currentPage: (state as BookLoaded).currentPage,
      ));
    }

    final result = await getBooks(GetBooksParams(
      page: event.page,
      limit: event.limit,
      genre: event.genre,
      searchQuery: event.searchQuery,
    ));

    result.fold(
      (failure) => emit(BookError(_mapFailureToMessage(failure))),
      (books) {
        if (event.page == 1) {
          emit(BookLoaded(
            books: books,
            hasReachedMax: books.length < event.limit,
            currentPage: event.page,
          ));
        } else {
          final currentState = state as BookLoaded;
          final allBooks = List.of(currentState.books)..addAll(books);
          emit(BookLoaded(
            books: allBooks,
            hasReachedMax: books.length < event.limit,
            currentPage: event.page,
          ));
        }
      },
    );
  }

  Future<void> _onGetBookDetail(
      GetBookDetailEvent event, Emitter<BookState> emit) async {
    emit(BookLoading());

    final result = await getBookDetail(GetBookDetailParams(bookId: event.bookId));

    result.fold(
      (failure) => emit(BookError(_mapFailureToMessage(failure))),
      (book) => emit(BookDetailLoaded(book)),
    );
  }

  Future<void> _onCreateBook(
      CreateBookEvent event, Emitter<BookState> emit) async {
    emit(BookLoading());

    final result = await createBook(CreateBookParams(
      title: event.title,
      description: event.description,
      genres: event.genres,
      coverImageUrl: event.coverImageUrl,
    ));

    result.fold(
      (failure) => emit(BookError(_mapFailureToMessage(failure))),
      (book) => emit(BookCreated(book)),
    );
  }

  Future<void> _onRefreshBooks(
      RefreshBooksEvent event, Emitter<BookState> emit) async {
    emit(BookLoading());
    add(const GetBooksEvent(page: 1));
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Server error occurred';
      case CacheFailure _:
        return 'Cache error occurred';
      case NetworkFailure _:
        return 'Network error occurred';
      default:
        return 'Unexpected error occurred';
    }
  }
}
