-- Check reading progress data for the user
-- Run this in Supabase SQL Editor

-- Check if reading_progress table exists and its structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'reading_progress' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check reading progress data for the user
SELECT * FROM public.reading_progress 
WHERE user_id = '9fd14ae7-0e04-4d50-aa9c-8f64fb473318';

-- Check if reading_state table exists and its structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'reading_state' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check reading_state data for the user (if table exists)
SELECT * FROM public.reading_state 
WHERE id = '9fd14ae7-0e04-4d50-aa9c-8f64fb473318';

-- Check bookmarks data for the user
SELECT * FROM public.bookmarks 
WHERE user_id = '9fd14ae7-0e04-4d50-aa9c-8f64fb473318';
