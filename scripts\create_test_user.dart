import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://qvlurdcrckbmzpyvntip.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2bHVyZGNyY2tibXpweXZudGlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MTA0NTksImV4cCI6MjA2NTk4NjQ1OX0.TSzAsqH72wtlrT6AcfN9s__RYjDuIrzaORSCnZRZyxQ',
  );

  final client = Supabase.instance.client;

  try {
    print('Creating test user...');
    
    // Sign up a test user
    final authResponse = await client.auth.signUp(
      email: '<EMAIL>',
      password: 'testpassword123',
      data: {'full_name': 'Test User'},
    );

    if (authResponse.user != null) {
      print('Test user created successfully!');
      print('User ID: ${authResponse.user!.id}');
      print('Email: ${authResponse.user!.email}');
      
      // Wait a bit for the user to be created
      await Future.delayed(const Duration(seconds: 2));
      
      // Check if user profile was created in users table
      final userProfile = await client
          .from('users')
          .select()
          .eq('id', authResponse.user!.id)
          .maybeSingle();
      
      if (userProfile != null) {
        print('User profile found in users table: $userProfile');
      } else {
        print('User profile not found, creating manually...');
        
        // Create user profile manually
        await client.from('users').insert({
          'id': authResponse.user!.id,
          'email': authResponse.user!.email!,
          'full_name': 'Test User',
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
        
        print('User profile created manually');
      }
      
      // Test creating a bookmark
      print('Testing bookmark creation...');
      await client.from('bookmarks').insert({
        'user_id': authResponse.user!.id,
        'book_id': 'test-book-id',
      });
      print('Bookmark created successfully!');
      
      // Test creating reading progress
      print('Testing reading progress creation...');
      await client.from('reading_progress').insert({
        'user_id': authResponse.user!.id,
        'book_id': 'test-book-id',
        'chapter_id': 'test-chapter-id',
        'progress_percentage': 25.0,
        'last_read_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      print('Reading progress created successfully!');
      
      print('\nTest completed successfully!');
      print('You can now sign in with:');
      print('Email: <EMAIL>');
      print('Password: testpassword123');
      
    } else {
      print('Failed to create test user');
    }
    
  } catch (e) {
    print('Error: $e');
  }
}
