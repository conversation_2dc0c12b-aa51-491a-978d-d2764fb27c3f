import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/usecases/get_chapters_by_book_id.dart';
import '../../domain/usecases/create_chapter.dart';
import '../../domain/usecases/update_chapter.dart';
import '../../domain/usecases/delete_chapter.dart';
import 'chapter_event.dart';
import 'chapter_state.dart';

class ChapterBloc extends Bloc<ChapterEvent, ChapterState> {
  final GetChaptersByBookId getChaptersByBookId;
  final CreateChapter createChapter;
  final UpdateChapter updateChapter;
  final DeleteChapter deleteChapter;

  Timer? _autoSaveTimer;

  ChapterBloc({
    required this.getChaptersByBookId,
    required this.createChapter,
    required this.updateChapter,
    required this.deleteChapter,
  }) : super(ChapterInitial()) {
    on<GetChaptersByBookIdRequested>(_onGetChaptersByBookId);
    on<CreateChapterRequested>(_onCreateChapter);
    on<UpdateChapterRequested>(_onUpdateChapter);
    on<DeleteChapterRequested>(_onDeleteChapter);
    on<AutoSaveChapterRequested>(_onAutoSaveChapter);
  }

  @override
  Future<void> close() {
    _autoSaveTimer?.cancel();
    return super.close();
  }

  Future<void> _onGetChaptersByBookId(
    GetChaptersByBookIdRequested event,
    Emitter<ChapterState> emit,
  ) async {
    emit(ChapterLoading());

    final result = await getChaptersByBookId(
      GetChaptersByBookIdParams(bookId: event.bookId),
    );

    result.fold(
      (failure) => emit(ChapterError(_mapFailureToMessage(failure))),
      (chapters) => emit(ChaptersLoaded(chapters)),
    );
  }

  Future<void> _onCreateChapter(
    CreateChapterRequested event,
    Emitter<ChapterState> emit,
  ) async {
    emit(ChapterLoading());

    final result = await createChapter(CreateChapterParams(
      bookId: event.bookId,
      chapterNumber: event.chapterNumber,
      title: event.title,
      content: event.content,
    ));

    result.fold(
      (failure) => emit(ChapterError(_mapFailureToMessage(failure))),
      (chapter) => emit(ChapterCreated(chapter)),
    );
  }

  Future<void> _onUpdateChapter(
    UpdateChapterRequested event,
    Emitter<ChapterState> emit,
  ) async {
    // Show saving state for manual saves
    if (event.title != null) {
      emit(ChapterSaving(state is ChapterLoaded 
          ? (state as ChapterLoaded).chapter 
          : state is ChapterUpdated 
              ? (state as ChapterUpdated).chapter
              : state is ChapterAutoSaved
                  ? (state as ChapterAutoSaved).chapter
                  : throw Exception('Invalid state for saving')));
    }

    final result = await updateChapter(UpdateChapterParams(
      chapterId: event.chapterId,
      title: event.title,
      content: event.content,
      wordCount: event.wordCount,
      status: event.status,
    ));

    result.fold(
      (failure) => emit(ChapterError(_mapFailureToMessage(failure))),
      (chapter) => emit(ChapterUpdated(chapter)),
    );
  }

  Future<void> _onDeleteChapter(
    DeleteChapterRequested event,
    Emitter<ChapterState> emit,
  ) async {
    emit(ChapterLoading());

    final result = await deleteChapter(
      DeleteChapterParams(chapterId: event.chapterId),
    );

    result.fold(
      (failure) => emit(ChapterError(_mapFailureToMessage(failure))),
      (_) => emit(ChapterDeleted(event.chapterId)),
    );
  }

  Future<void> _onAutoSaveChapter(
    AutoSaveChapterRequested event,
    Emitter<ChapterState> emit,
  ) async {
    // Cancel previous timer
    _autoSaveTimer?.cancel();

    // Set new timer for auto-save (debounce)
    _autoSaveTimer = Timer(const Duration(seconds: 2), () async {
      final result = await updateChapter(UpdateChapterParams(
        chapterId: event.chapterId,
        content: event.content,
        wordCount: event.wordCount,
      ));

      result.fold(
        (failure) => emit(ChapterError(_mapFailureToMessage(failure))),
        (chapter) => emit(ChapterAutoSaved(chapter)),
      );
    });
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Server error occurred';
      case CacheFailure _:
        return 'Cache error occurred';
      case NetworkFailure _:
        return 'Network error occurred';
      default:
        return 'Unexpected error occurred';
    }
  }
}
