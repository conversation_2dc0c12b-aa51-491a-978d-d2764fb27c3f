import 'package:get_it/get_it.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Core
import '../network/network_info.dart';

// Books Feature
import '../../features/books/data/datasources/book_local_data_source.dart';
import '../../features/books/data/datasources/book_remote_data_source.dart';
import '../../features/books/data/datasources/library_remote_data_source.dart';
import '../../features/books/data/repositories/book_repository_impl.dart';
import '../../features/books/data/repositories/library_repository_impl.dart';
import '../../features/books/domain/repositories/book_repository.dart';
import '../../features/books/domain/repositories/library_repository.dart';
import '../../features/books/domain/usecases/get_books.dart';
import '../../features/books/domain/usecases/create_book.dart';
import '../../features/books/domain/usecases/get_book_detail.dart';
import '../../features/books/domain/usecases/update_book.dart';
import '../../features/books/domain/usecases/delete_book.dart';
import '../../features/books/domain/usecases/get_my_books.dart';
import '../../features/books/domain/usecases/search_books.dart';
import '../../features/books/domain/usecases/get_user_library_books.dart';
import '../../features/books/domain/usecases/get_chapters_by_book_id.dart';
import '../../features/books/domain/usecases/create_chapter.dart';
import '../../features/books/domain/usecases/update_chapter.dart';
import '../../features/books/domain/usecases/delete_chapter.dart';
import '../../features/books/presentation/bloc/book_bloc.dart';
import '../../features/books/presentation/bloc/library_bloc.dart';
import '../../features/books/presentation/bloc/chapter_bloc.dart';
import '../../features/books/domain/repositories/chapter_repository.dart';
import '../../features/books/data/repositories/chapter_repository_impl.dart';
import '../../features/books/data/datasources/chapter_remote_data_source.dart';

final sl = GetIt.instance;

Future<void> init() async {
  //! Features - Books
  // Bloc
  sl.registerFactory(
    () => BookBloc(
      getBooks: sl(),
      getBookDetail: sl(),
      createBook: sl(),
      updateBook: sl(),
      deleteBook: sl(),
      getMyBooks: sl(),
      searchBooks: sl(),
    ),
  );

  // Chapter Bloc
  sl.registerFactory(
    () => ChapterBloc(
      getChaptersByBookId: sl(),
      createChapter: sl(),
      updateChapter: sl(),
      deleteChapter: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetBooks(sl()));
  sl.registerLazySingleton(() => CreateBook(sl()));
  sl.registerLazySingleton(() => GetBookDetail(sl()));
  sl.registerLazySingleton(() => UpdateBook(sl()));
  sl.registerLazySingleton(() => DeleteBook(sl()));
  sl.registerLazySingleton(() => GetMyBooks(sl()));
  sl.registerLazySingleton(() => SearchBooks(sl()));

  // Chapter use cases
  sl.registerLazySingleton(() => GetChaptersByBookId(sl()));
  sl.registerLazySingleton(() => CreateChapter(sl()));
  sl.registerLazySingleton(() => UpdateChapter(sl()));
  sl.registerLazySingleton(() => DeleteChapter(sl()));

  // Repository
  sl.registerLazySingleton<BookRepository>(
    () => BookRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Chapter Repository
  sl.registerLazySingleton<ChapterRepository>(
    () => ChapterRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
  );

  // Data sources
  sl.registerLazySingleton<BookRemoteDataSource>(
    () => BookRemoteDataSourceImpl(client: sl()),
  );

  sl.registerLazySingleton<BookLocalDataSource>(
    () => BookLocalDataSourceImpl(),
  );

  //! Features - Library
  // Bloc
  sl.registerFactory(() => LibraryBloc(getUserLibraryBooks: sl()));

  // Use cases
  sl.registerLazySingleton(() => GetUserLibraryBooks(sl()));

  // Repository
  sl.registerLazySingleton<LibraryRepository>(
    () => LibraryRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
  );

  // Data sources
  sl.registerLazySingleton<LibraryRemoteDataSource>(
    () => LibraryRemoteDataSourceImpl(supabase: sl()),
  );

  //! Core
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());

  //! External
  sl.registerLazySingleton(() => Supabase.instance.client);
}
