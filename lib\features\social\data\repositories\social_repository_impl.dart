import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/follow.dart';
import '../../domain/entities/book_like.dart';
import '../../domain/entities/comment.dart';
import '../../domain/entities/reading_list.dart';
import '../../domain/repositories/social_repository.dart';
import '../datasources/social_remote_data_source.dart';
import '../../../auth/domain/entities/user.dart';

class SocialRepositoryImpl implements SocialRepository {
  final SocialRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  SocialRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, Follow>> followUser(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final follow = await remoteDataSource.followUser(userId);
        return Right(follow.toEntity());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> unfollowUser(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.unfollowUser(userId);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, bool>> isFollowing(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final isFollowing = await remoteDataSource.isFollowing(userId);
        return Right(isFollowing);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getFollowers(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final followers = await remoteDataSource.getFollowers(userId);
        return Right(followers.map((user) => user.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getFollowing(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final following = await remoteDataSource.getFollowing(userId);
        return Right(following.map((user) => user.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, BookLike>> likeBook(String bookId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.likeBook(bookId);
        // Create a dummy BookLike since the remote data source doesn't return one
        final bookLike = BookLike(
          id: '', // Will be generated by database
          userId: '', // Will be set by database
          bookId: bookId,
          createdAt: DateTime.now(),
        );
        return Right(bookLike);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> unlikeBook(String bookId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.unlikeBook(bookId);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, bool>> isBookLiked(String bookId) async {
    if (await networkInfo.isConnected) {
      try {
        final isLiked = await remoteDataSource.isBookLiked(bookId);
        return Right(isLiked);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getBookLikers(String bookId) async {
    if (await networkInfo.isConnected) {
      try {
        final likers = await remoteDataSource.getBookLikers(bookId);
        return Right(likers.map((user) => user.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Comment>> addComment({
    required String bookId,
    String? chapterId,
    required String content,
    String? parentCommentId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final comment = await remoteDataSource.addComment(
          bookId: bookId,
          chapterId: chapterId,
          content: content,
          parentCommentId: parentCommentId,
        );
        return Right(comment.toEntity());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Comment>> updateComment({
    required String commentId,
    required String content,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final comment = await remoteDataSource.updateComment(
          commentId: commentId,
          content: content,
        );
        return Right(comment.toEntity());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteComment(String commentId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteComment(commentId);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<Comment>>> getBookComments(String bookId) async {
    if (await networkInfo.isConnected) {
      try {
        final comments = await remoteDataSource.getBookComments(bookId);
        return Right(comments.map((comment) => comment.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<Comment>>> getChapterComments(String chapterId) async {
    if (await networkInfo.isConnected) {
      try {
        final comments = await remoteDataSource.getChapterComments(chapterId);
        return Right(comments.map((comment) => comment.toEntity()).toList());
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  // Reading Lists methods - placeholder implementations
  @override
  Future<Either<Failure, ReadingList>> createReadingList({
    required String name,
    String? description,
    bool isPublic = true,
  }) async {
    // TODO: Implement reading list functionality
    return const Left(ServerFailure('Reading lists not implemented yet'));
  }

  @override
  Future<Either<Failure, ReadingList>> updateReadingList({
    required String listId,
    String? name,
    String? description,
    bool? isPublic,
  }) async {
    // TODO: Implement reading list functionality
    return const Left(ServerFailure('Reading lists not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> deleteReadingList(String listId) async {
    // TODO: Implement reading list functionality
    return const Left(ServerFailure('Reading lists not implemented yet'));
  }

  @override
  Future<Either<Failure, List<ReadingList>>> getUserReadingLists(String userId) async {
    // TODO: Implement reading list functionality
    return const Left(ServerFailure('Reading lists not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> addBookToReadingList({
    required String listId,
    required String bookId,
  }) async {
    // TODO: Implement reading list functionality
    return const Left(ServerFailure('Reading lists not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> removeBookFromReadingList({
    required String listId,
    required String bookId,
  }) async {
    // TODO: Implement reading list functionality
    return const Left(ServerFailure('Reading lists not implemented yet'));
  }
}
