import 'package:equatable/equatable.dart';

abstract class SocialEvent extends Equatable {
  const SocialEvent();

  @override
  List<Object?> get props => [];
}

// Follow Events
class FollowUserRequested extends SocialEvent {
  final String userId;

  const FollowUserRequested(this.userId);

  @override
  List<Object> get props => [userId];
}

class UnfollowUserRequested extends SocialEvent {
  final String userId;

  const UnfollowUserRequested(this.userId);

  @override
  List<Object> get props => [userId];
}

class CheckFollowStatusRequested extends SocialEvent {
  final String userId;

  const CheckFollowStatusRequested(this.userId);

  @override
  List<Object> get props => [userId];
}

class GetFollowersRequested extends SocialEvent {
  final String userId;

  const GetFollowersRequested(this.userId);

  @override
  List<Object> get props => [userId];
}

class GetFollowingRequested extends SocialEvent {
  final String userId;

  const GetFollowingRequested(this.userId);

  @override
  List<Object> get props => [userId];
}

// Like Events
class LikeBookRequested extends SocialEvent {
  final String bookId;

  const LikeBookRequested(this.bookId);

  @override
  List<Object> get props => [bookId];
}

class UnlikeBookRequested extends SocialEvent {
  final String bookId;

  const UnlikeBookRequested(this.bookId);

  @override
  List<Object> get props => [bookId];
}

class CheckBookLikeStatusRequested extends SocialEvent {
  final String bookId;

  const CheckBookLikeStatusRequested(this.bookId);

  @override
  List<Object> get props => [bookId];
}

// Comment Events
class AddCommentRequested extends SocialEvent {
  final String bookId;
  final String? chapterId;
  final String content;
  final String? parentCommentId;

  const AddCommentRequested({
    required this.bookId,
    this.chapterId,
    required this.content,
    this.parentCommentId,
  });

  @override
  List<Object?> get props => [bookId, chapterId, content, parentCommentId];
}

class UpdateCommentRequested extends SocialEvent {
  final String commentId;
  final String content;

  const UpdateCommentRequested({
    required this.commentId,
    required this.content,
  });

  @override
  List<Object> get props => [commentId, content];
}

class DeleteCommentRequested extends SocialEvent {
  final String commentId;

  const DeleteCommentRequested(this.commentId);

  @override
  List<Object> get props => [commentId];
}

class GetBookCommentsRequested extends SocialEvent {
  final String bookId;

  const GetBookCommentsRequested(this.bookId);

  @override
  List<Object> get props => [bookId];
}

class GetChapterCommentsRequested extends SocialEvent {
  final String chapterId;

  const GetChapterCommentsRequested(this.chapterId);

  @override
  List<Object> get props => [chapterId];
}
