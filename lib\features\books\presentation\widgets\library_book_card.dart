import 'package:flutter/material.dart';
import '../pages/book_detail_page.dart';

class LibraryBookCard extends StatelessWidget {
  final Map<String, dynamic> book;

  const LibraryBookCard({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BookDetailPage(
              bookId: book['id'],
              title: book['title'],
              authorName: book['author_name'],
              coverImageUrl: book['cover_image_url'],
            ),
          ),
        );
      },
      child: Container(
        width: 120, // Fixed width
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Book Cover
            AspectRatio(
              aspectRatio: 0.7,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child:
                    book['cover_image_url'] != null &&
                        book['cover_image_url'].toString().isNotEmpty
                    ? Image.network(
                        book['cover_image_url'],
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.book,
                            size: 32,
                            color: Colors.grey[600],
                          ),
                        ),
                      )
                    : Container(
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.book,
                          size: 32,
                          color: Colors.grey[600],
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 6),
            // Title
            Expanded(
              child: Text(
                book['title'] ?? 'Untitled',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
