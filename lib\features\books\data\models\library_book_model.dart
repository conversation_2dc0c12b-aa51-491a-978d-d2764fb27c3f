import '../../domain/entities/library_book.dart';

class LibraryBookModel extends LibraryBook {
  const LibraryBookModel({
    required super.id,
    required super.title,
    required super.description,
    required super.authorId,
    required super.authorName,
    required super.coverImageUrl,
    required super.genres,
    required super.totalChapters,
    required super.isCompleted,
    required super.isPublished,
    required super.createdAt,
    required super.updatedAt,
    required super.views,
    required super.likes,
    super.lastReadChapterId,
    super.lastReadPage,
    super.lastReadAt,
    required super.readingProgress,
  });

  factory LibraryBookModel.fromJson(Map<String, dynamic> json) {
    return LibraryBookModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      authorId: json['author_id'] as String,
      authorName: json['author_name'] as String,
      coverImageUrl: json['cover_image_url'] as String? ?? '',
      genres: (json['genres'] as List<dynamic>).cast<String>(),
      totalChapters: json['total_chapters'] as int? ?? 0,
      isCompleted: json['is_completed'] as bool? ?? false,
      isPublished: json['is_published'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      views: json['views'] as int? ?? 0,
      likes: json['likes'] as int? ?? 0,
      lastReadChapterId: json['last_read_chapter'] as String?,
      lastReadPage: json['last_read_page'] as int?,
      lastReadAt: json['last_read_at'] != null
          ? DateTime.parse(json['last_read_at'] as String)
          : null,
      readingProgress: _calculateReadingProgress(
        totalChapters: json['total_chapters'] as int? ?? 0,
        lastReadChapter: json['last_read_chapter'] as String?,
        lastReadPage: json['last_read_page'] as int?,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'author_id': authorId,
      'author_name': authorName,
      'cover_image_url': coverImageUrl,
      'genres': genres,
      'total_chapters': totalChapters,
      'is_completed': isCompleted,
      'is_published': isPublished,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'views': views,
      'likes': likes,
      'last_read_chapter': lastReadChapterId,
      'last_read_page': lastReadPage,
      'last_read_at': lastReadAt?.toIso8601String(),
    };
  }

  static double _calculateReadingProgress({
    required int totalChapters,
    String? lastReadChapter,
    int? lastReadPage,
  }) {
    if (totalChapters == 0 || lastReadChapter == null) {
      return 0.0;
    }

    // Extract chapter number from chapter ID (assuming format like 'chapter_1')
    final chapterNumber = int.tryParse(lastReadChapter.split('_').last) ?? 0;

    // Calculate basic progress based on chapters
    double progress = (chapterNumber / totalChapters) * 100;

    // Adjust progress based on page if available
    if (lastReadPage != null) {
      // Assume each chapter has 100 pages for simplicity
      const pagesPerChapter = 100;
      final pageProgress = lastReadPage / pagesPerChapter;
      progress = ((chapterNumber - 1 + pageProgress) / totalChapters) * 100;
    }

    return progress.clamp(0.0, 100.0);
  }
}
