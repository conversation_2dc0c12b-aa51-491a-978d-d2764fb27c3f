import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://qvlurdcrckbmzpyvntip.supabase.co',
    anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2bHVyZGNyY2tibXpweXZudGlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MTA0NTksImV4cCI6MjA2NTk4NjQ1OX0.TSzAsqH72wtlrT6AcfN9s__RYjDuIrzaORSCnZRZyxQ',
  );

  final client = Supabase.instance.client;

  // Sample book data
  final books = [
    {
      'title': 'The Great Adventure',
      'description': 'An epic tale of courage and discovery in a magical world filled with wonder and danger.',
      'author_id': 'sample-author-1',
      'author_name': '<PERSON>',
      'status': 'published',
      'genre': 'Fantasy',
      'tags': ['adventure', 'magic', 'fantasy'],
      'total_chapters': 15,
      'total_words': 45000,
      'cover_image_url': 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
      'views': 1250,
      'likes': 89,
    },
    {
      'title': 'Mystery of the Lost City',
      'description': 'A thrilling mystery that takes readers on a journey through ancient ruins and hidden secrets.',
      'author_id': 'sample-author-2',
      'author_name': '<PERSON>',
      'status': 'published',
      'genre': 'Mystery',
      'tags': ['mystery', 'adventure', 'archaeology'],
      'total_chapters': 12,
      'total_words': 38000,
      'cover_image_url': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'views': 890,
      'likes': 67,
    },
    {
      'title': 'Love in the Digital Age',
      'description': 'A contemporary romance exploring relationships in our connected world.',
      'author_id': 'sample-author-3',
      'author_name': 'Emily Chen',
      'status': 'published',
      'genre': 'Romance',
      'tags': ['romance', 'contemporary', 'technology'],
      'total_chapters': 18,
      'total_words': 52000,
      'cover_image_url': 'https://images.unsplash.com/photo-1516979187457-637abb4f9353?w=400',
      'views': 2100,
      'likes': 156,
    },
    {
      'title': 'The Science of Tomorrow',
      'description': 'A fascinating exploration of cutting-edge scientific discoveries and their implications.',
      'author_id': 'sample-author-4',
      'author_name': 'Dr. Michael Brown',
      'status': 'published',
      'genre': 'Science Fiction',
      'tags': ['sci-fi', 'science', 'future'],
      'total_chapters': 10,
      'total_words': 35000,
      'cover_image_url': 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400',
      'views': 756,
      'likes': 43,
    },
    {
      'title': 'Cooking with Passion',
      'description': 'A collection of delicious recipes and culinary adventures from around the world.',
      'author_id': 'sample-author-5',
      'author_name': 'Chef Maria Rodriguez',
      'status': 'published',
      'genre': 'Cookbook',
      'tags': ['cooking', 'recipes', 'food'],
      'total_chapters': 8,
      'total_words': 25000,
      'cover_image_url': 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400',
      'views': 1890,
      'likes': 234,
    },
    {
      'title': 'The Art of Mindfulness',
      'description': 'A guide to finding peace and clarity in our busy modern lives through mindfulness practices.',
      'author_id': 'sample-author-6',
      'author_name': 'Lisa Thompson',
      'status': 'published',
      'genre': 'Self-Help',
      'tags': ['mindfulness', 'meditation', 'wellness'],
      'total_chapters': 14,
      'total_words': 42000,
      'cover_image_url': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
      'views': 1456,
      'likes': 98,
    },
  ];

  try {
    print('Adding dummy books to database...');
    
    for (final book in books) {
      await client.from('books').insert(book);
      print('Added: ${book['title']}');
    }
    
    print('Successfully added ${books.length} books to the database!');
    
    // Verify the data was added
    final result = await client.from('books').select().count();
    print('Total books in database: $result');
    
  } catch (e) {
    print('Error adding books: $e');
  }
}
