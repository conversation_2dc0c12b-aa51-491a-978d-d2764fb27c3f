
enum BlockType {
  text,
  heading1,
  heading2,
  heading3,
  bulletList,
  numberedList,
  quote,
  image,
}

class EditorBlock {
  final String id;
  final BlockType type;
  String content;
  Map<String, dynamic>? attributes; // For formatting, links, etc.

  EditorBlock({
    required this.id,
    required this.type,
    required this.content,
    this.attributes,
  });

  factory EditorBlock.empty({required BlockType type}) {
    return EditorBlock(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: type,
      content: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'content': content,
      'attributes': attributes,
    };
  }

  factory EditorBlock.fromJson(Map<String, dynamic> json) {
    return EditorBlock(
      id: json['id'],
      type: BlockType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => BlockType.text,
      ),
      content: json['content'],
      attributes: json['attributes'],
    );
  }
}

class BlockAttributes {
  static const bold = 'bold';
  static const italic = 'italic';
  static const underline = 'underline';
  static const link = 'link';
}
