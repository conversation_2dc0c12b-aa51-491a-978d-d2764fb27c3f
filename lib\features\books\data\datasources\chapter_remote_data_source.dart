import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/failures.dart';
import '../models/chapter_model.dart';

abstract class ChapterRemoteDataSource {
  Future<List<ChapterModel>> getChaptersByBookId(String bookId);
  Future<ChapterModel> getChapterById(String chapterId);
  Future<ChapterModel> createChapter({
    required String bookId,
    required int chapterNumber,
    required String title,
    String? content,
  });
  Future<ChapterModel> updateChapter({
    required String chapterId,
    String? title,
    String? content,
    int? wordCount,
    String? status,
  });
  Future<void> deleteChapter(String chapterId);
  Future<ChapterModel> reorderChapter({
    required String chapterId,
    required int newChapterNumber,
  });
}

class ChapterRemoteDataSourceImpl implements ChapterRemoteDataSource {
  final SupabaseClient client;

  ChapterRemoteDataSourceImpl({required this.client});

  @override
  Future<List<ChapterModel>> getChaptersByBookId(String bookId) async {
    try {
      final response = await client
          .from('chapters')
          .select()
          .eq('book_id', bookId)
          .order('chapter_number');

      return (response as List)
          .map((chapter) => ChapterModel.fromSupabase(chapter))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<ChapterModel> getChapterById(String chapterId) async {
    try {
      final response = await client
          .from('chapters')
          .select()
          .eq('id', chapterId)
          .single();

      return ChapterModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<ChapterModel> createChapter({
    required String bookId,
    required int chapterNumber,
    required String title,
    String? content,
  }) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      final response = await client.from('chapters').insert({
        'book_id': bookId,
        'chapter_number': chapterNumber,
        'title': title,
        'content': content ?? '',
        'word_count': 0,
        'status': 'draft',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).select().single();

      return ChapterModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<ChapterModel> updateChapter({
    required String chapterId,
    String? title,
    String? content,
    int? wordCount,
    String? status,
  }) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (title != null) updateData['title'] = title;
      if (content != null) updateData['content'] = content;
      if (wordCount != null) updateData['word_count'] = wordCount;
      if (status != null) updateData['status'] = status;

      final response = await client
          .from('chapters')
          .update(updateData)
          .eq('id', chapterId)
          .select()
          .single();

      return ChapterModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<void> deleteChapter(String chapterId) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      await client.from('chapters').delete().eq('id', chapterId);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<ChapterModel> reorderChapter({
    required String chapterId,
    required int newChapterNumber,
  }) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      final response = await client
          .from('chapters')
          .update({
            'chapter_number': newChapterNumber,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', chapterId)
          .select()
          .single();

      return ChapterModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }
}
