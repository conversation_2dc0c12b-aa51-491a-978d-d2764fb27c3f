import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/failures.dart';
import '../../../auth/data/models/user_model.dart';
import '../models/follow_model.dart';
import '../models/comment_model.dart';

abstract class SocialRemoteDataSource {
  // Follow System
  Future<FollowModel> followUser(String userId);
  Future<void> unfollowUser(String userId);
  Future<bool> isFollowing(String userId);
  Future<List<UserModel>> getFollowers(String userId);
  Future<List<UserModel>> getFollowing(String userId);

  // Book Likes
  Future<void> likeBook(String bookId);
  Future<void> unlikeBook(String bookId);
  Future<bool> isBookLiked(String bookId);
  Future<List<UserModel>> getBookLikers(String bookId);

  // Comments
  Future<CommentModel> addComment({
    required String bookId,
    String? chapterId,
    required String content,
    String? parentCommentId,
  });
  Future<CommentModel> updateComment({
    required String commentId,
    required String content,
  });
  Future<void> deleteComment(String commentId);
  Future<List<CommentModel>> getBookComments(String bookId);
  Future<List<CommentModel>> getChapterComments(String chapterId);
}

class SocialRemoteDataSourceImpl implements SocialRemoteDataSource {
  final SupabaseClient client;

  SocialRemoteDataSourceImpl({required this.client});

  @override
  Future<FollowModel> followUser(String userId) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) throw const ServerFailure('User not authenticated');

      final response = await client.from('follows').insert({
        'follower_id': currentUserId,
        'following_id': userId,
      }).select().single();

      return FollowModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<void> unfollowUser(String userId) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) throw const ServerFailure('User not authenticated');

      await client
          .from('follows')
          .delete()
          .eq('follower_id', currentUserId)
          .eq('following_id', userId);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<bool> isFollowing(String userId) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) return false;

      final response = await client
          .from('follows')
          .select('id')
          .eq('follower_id', currentUserId)
          .eq('following_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<UserModel>> getFollowers(String userId) async {
    try {
      final response = await client
          .from('follows')
          .select('follower_id, users!follows_follower_id_fkey(*)')
          .eq('following_id', userId);

      return (response as List)
          .map((follow) => UserModel.fromSupabase(follow['users']))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<List<UserModel>> getFollowing(String userId) async {
    try {
      final response = await client
          .from('follows')
          .select('following_id, users!follows_following_id_fkey(*)')
          .eq('follower_id', userId);

      return (response as List)
          .map((follow) => UserModel.fromSupabase(follow['users']))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<void> likeBook(String bookId) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) throw const ServerFailure('User not authenticated');

      await client.from('book_likes').insert({
        'user_id': currentUserId,
        'book_id': bookId,
      });
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<void> unlikeBook(String bookId) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) throw const ServerFailure('User not authenticated');

      await client
          .from('book_likes')
          .delete()
          .eq('user_id', currentUserId)
          .eq('book_id', bookId);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<bool> isBookLiked(String bookId) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) return false;

      final response = await client
          .from('book_likes')
          .select('id')
          .eq('user_id', currentUserId)
          .eq('book_id', bookId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<UserModel>> getBookLikers(String bookId) async {
    try {
      final response = await client
          .from('book_likes')
          .select('user_id, users(*)')
          .eq('book_id', bookId);

      return (response as List)
          .map((like) => UserModel.fromSupabase(like['users']))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<CommentModel> addComment({
    required String bookId,
    String? chapterId,
    required String content,
    String? parentCommentId,
  }) async {
    try {
      final currentUserId = client.auth.currentUser?.id;
      if (currentUserId == null) throw const ServerFailure('User not authenticated');

      final response = await client.from('comments').insert({
        'user_id': currentUserId,
        'book_id': bookId,
        'chapter_id': chapterId,
        'content': content,
        'parent_comment_id': parentCommentId,
      }).select('*, users(*)').single();

      return CommentModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<CommentModel> updateComment({
    required String commentId,
    required String content,
  }) async {
    try {
      final response = await client
          .from('comments')
          .update({
            'content': content,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', commentId)
          .select('*, users(*)')
          .single();

      return CommentModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<void> deleteComment(String commentId) async {
    try {
      await client.from('comments').delete().eq('id', commentId);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<List<CommentModel>> getBookComments(String bookId) async {
    try {
      final response = await client
          .from('comments')
          .select('*, users(*)')
          .eq('book_id', bookId)
          .isFilter('parent_comment_id', null)
          .order('created_at', ascending: false);

      return (response as List)
          .map((comment) => CommentModel.fromSupabase(comment))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<List<CommentModel>> getChapterComments(String chapterId) async {
    try {
      final response = await client
          .from('comments')
          .select('*, users(*)')
          .eq('chapter_id', chapterId)
          .isFilter('parent_comment_id', null)
          .order('created_at', ascending: false);

      return (response as List)
          .map((comment) => CommentModel.fromSupabase(comment))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }
}
