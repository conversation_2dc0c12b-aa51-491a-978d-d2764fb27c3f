import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signInWithEmail({
    required String email,
    required String password,
  });

  Future<UserModel> signUpWithEmail({
    required String email,
    required String password,
    required String fullName,
  });

  Future<UserModel> signInWithGoogle();

  Future<void> signOut();

  Future<UserModel?> getCurrentUser();

  Future<UserModel> updateUserProfile({
    required String userId,
    String? fullName,
    String? username,
    String? bio,
    String? avatarUrl,
  });

  Future<UserModel> getUserById(String userId);

  Future<List<UserModel>> searchUsers(String query);

  Stream<UserModel?> get authStateChanges;
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final supabase.SupabaseClient client;

  AuthRemoteDataSourceImpl({required this.client});

  @override
  Future<UserModel> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Sign in failed');
      }

      // Get user profile from users table
      final userProfile = await client
          .from('users')
          .select()
          .eq('id', response.user!.id)
          .single();

      return UserModel.fromSupabase(userProfile);
    } catch (e) {
      throw Exception('Failed to sign in: $e');
    }
  }

  @override
  Future<UserModel> signUpWithEmail({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName},
      );

      if (response.user == null) {
        throw Exception('Sign up failed');
      }

      // The user profile should be automatically created by the trigger
      // Wait a bit and then fetch the profile
      await Future.delayed(const Duration(milliseconds: 500));

      final userProfile = await client
          .from('users')
          .select()
          .eq('id', response.user!.id)
          .single();

      return UserModel.fromSupabase(userProfile);
    } catch (e) {
      throw Exception('Failed to sign up: $e');
    }
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    try {
      final response = await client.auth.signInWithOAuth(
        supabase.OAuthProvider.google,
      );

      if (response == false) {
        throw Exception('Google sign in failed');
      }

      // Wait for auth state to update
      await Future.delayed(const Duration(seconds: 1));

      final user = client.auth.currentUser;
      if (user == null) {
        throw Exception('No user after Google sign in');
      }

      // Get user profile from users table
      final userProfile = await client
          .from('users')
          .select()
          .eq('id', user.id)
          .single();

      return UserModel.fromSupabase(userProfile);
    } catch (e) {
      throw Exception('Failed to sign in with Google: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = client.auth.currentUser;
      if (user == null) return null;

      final userProfile = await client
          .from('users')
          .select()
          .eq('id', user.id)
          .single();

      return UserModel.fromSupabase(userProfile);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<UserModel> updateUserProfile({
    required String userId,
    String? fullName,
    String? username,
    String? bio,
    String? avatarUrl,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (fullName != null) updateData['full_name'] = fullName;
      if (username != null) updateData['username'] = username;
      if (bio != null) updateData['bio'] = bio;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

      final response = await client
          .from('users')
          .update(updateData)
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromSupabase(response);
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  @override
  Future<UserModel> getUserById(String userId) async {
    try {
      final response = await client
          .from('users')
          .select()
          .eq('id', userId)
          .single();

      return UserModel.fromSupabase(response);
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  @override
  Future<List<UserModel>> searchUsers(String query) async {
    try {
      final response = await client
          .from('users')
          .select()
          .or('full_name.ilike.%$query%,username.ilike.%$query%')
          .limit(20);

      return response.map((user) => UserModel.fromSupabase(user)).toList();
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  @override
  Stream<UserModel?> get authStateChanges {
    return client.auth.onAuthStateChange.asyncMap((authState) async {
      final user = authState.session?.user;
      if (user == null) return null;

      try {
        final userProfile = await client
            .from('users')
            .select()
            .eq('id', user.id)
            .single();

        return UserModel.fromSupabase(userProfile);
      } catch (e) {
        return null;
      }
    });
  }
}
