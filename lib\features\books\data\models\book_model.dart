import '../../domain/entities/book.dart';

class BookModel extends Book {
  const BookModel({
    required super.id,
    required super.title,
    required super.description,
    required super.authorId,
    required super.authorName,
    required super.coverImageUrl,
    required super.genres,
    required super.totalChapters,
    required super.isCompleted,
    required super.isPublished,
    required super.createdAt,
    required super.updatedAt,
    required super.views,
    required super.likes,
  });

  factory BookModel.fromSupabase(Map<String, dynamic> map) {
    return BookModel(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      authorId: map['author_id'] as String,
      authorName: map['author_name'] as String,
      coverImageUrl: map['cover_image_url'] as String? ?? '',
      genres: List<String>.from(map['genres'] as List? ?? []),
      totalChapters: map['total_chapters'] as int? ?? 0,
      isCompleted: map['is_completed'] as bool? ?? false,
      isPublished: map['is_published'] as bool? ?? false,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      views: map['views'] as int? ?? 0,
      likes: map['likes'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toSupabase() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'author_id': authorId,
      'author_name': authorName,
      'cover_image_url': coverImageUrl,
      'genres': genres,
      'total_chapters': totalChapters,
      'is_completed': isCompleted,
      'is_published': isPublished,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'views': views,
      'likes': likes,
    };
  }

  factory BookModel.fromEntity(Book book) {
    return BookModel(
      id: book.id,
      title: book.title,
      description: book.description,
      authorId: book.authorId,
      authorName: book.authorName,
      coverImageUrl: book.coverImageUrl,
      genres: book.genres,
      totalChapters: book.totalChapters,
      isCompleted: book.isCompleted,
      isPublished: book.isPublished,
      createdAt: book.createdAt,
      updatedAt: book.updatedAt,
      views: book.views,
      likes: book.likes,
    );
  }

  Book toEntity() {
    return Book(
      id: id,
      title: title,
      description: description,
      authorId: authorId,
      authorName: authorName,
      coverImageUrl: coverImageUrl,
      genres: genres,
      totalChapters: totalChapters,
      isCompleted: isCompleted,
      isPublished: isPublished,
      createdAt: createdAt,
      updatedAt: updatedAt,
      views: views,
      likes: likes,
    );
  }
}
