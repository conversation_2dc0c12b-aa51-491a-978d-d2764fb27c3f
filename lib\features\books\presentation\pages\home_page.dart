import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../widgets/featured_book_card.dart';
import '../widgets/small_book_card.dart';
import '../widgets/category_card.dart';
import '../widgets/section_header.dart';
import 'search_page.dart';
import 'book_detail_page.dart';
import 'books_list_page.dart';
import 'category_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late Future<List<Map<String, dynamic>>> _future;

  @override
  void initState() {
    super.initState();
    _future = Supabase.instance.client
        .from('books')
        .select(); // Simplified query without limit or order
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {
            _future = Supabase.instance.client
                .from('books')
                .select(); // Keep it consistent with initState
          });
        },
        child: FutureBuilder(
          future: _future,
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const Center(child: CircularProgressIndicator());
            }
            final books = snapshot.data!;
            if (books.isEmpty) {
              return const Center(child: Text('No books found'));
            }

            // For demo, we'll use the first book as featured
            final featuredBook = books.first;
            // Show more books in each section - use all available books
            final recommendedBooks = books.skip(1).toList();
            final trendingBooks = books.toList(); // Show all books in trending

            return CustomScrollView(
              slivers: [
                // Custom App Bar
                SliverAppBar(
                  floating: true,
                  title: const Text('ReadThis'),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SearchPage(),
                          ),
                        );
                      },
                    ),
                  ],
                ),

                // Featured Book Section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: FeaturedBookCard(
                      title: featuredBook['title'] ?? 'Untitled',
                      description:
                          featuredBook['description'] ?? 'No description',
                      authorName: featuredBook['author_name'] ?? 'Anonymous',
                      genres: List<String>.from(featuredBook['genres'] ?? []),
                      coverImageUrl: featuredBook['cover_image_url'] ?? '',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BookDetailPage(
                              bookId: featuredBook['id'],
                              title: featuredBook['title'],
                              authorName: featuredBook['author_name'],
                              coverImageUrl: featuredBook['cover_image_url'],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

                // Recommended Books Section
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SectionHeader(
                        title: 'Rekomendasi',
                        onSeeAll: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const BooksListPage(
                                title: 'Recommended Books',
                                filterType: 'recommended',
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(
                        height: 240,
                        child: ListView.builder(
                          padding: const EdgeInsets.only(left: 16),
                          scrollDirection: Axis.horizontal,
                          clipBehavior: Clip.none,
                          itemCount: () {
                            // Calculate max books that can fit + 1 for peek effect
                            final screenWidth = MediaQuery.of(
                              context,
                            ).size.width;
                            const cardWidth = 120.0; // Fixed card width
                            const padding = 16.0; // Left padding only
                            const spacing = 16.0; // Margin between cards

                            final availableWidth = screenWidth - padding;
                            final maxBooks =
                                ((availableWidth + spacing) /
                                        (cardWidth + spacing))
                                    .floor();

                            // Show max books that fit + 1 for peek effect, but not more than total books
                            final showCount = maxBooks + 1;
                            return recommendedBooks.length > showCount
                                ? showCount
                                : recommendedBooks.length;
                          }(),
                          itemBuilder: (context, index) {
                            final book = recommendedBooks[index];
                            return SmallBookCard(
                              title: book['title'] ?? 'Untitled',
                              authorName: book['author_name'] ?? 'Anonymous',
                              coverImageUrl: book['cover_image_url'] ?? '',
                              views: book['views'] ?? 0,
                              likes: book['likes'] ?? 0,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => BookDetailPage(
                                      bookId: book['id'],
                                      title: book['title'],
                                      authorName: book['author_name'],
                                      coverImageUrl: book['cover_image_url'],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Trending Books Section
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SectionHeader(
                        title: 'Buku Terlaris',
                        onSeeAll: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const BooksListPage(
                                title: 'Trending Books',
                                filterType: 'trending',
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(
                        height: 240,
                        child: ListView.builder(
                          padding: const EdgeInsets.only(left: 16),
                          scrollDirection: Axis.horizontal,
                          clipBehavior: Clip.none,
                          itemCount: () {
                            final screenWidth = MediaQuery.of(
                              context,
                            ).size.width;
                            const cardWidth = 120.0;
                            const padding = 16.0;
                            const spacing = 16.0;

                            final availableWidth = screenWidth - padding;
                            final maxBooks =
                                ((availableWidth + spacing) /
                                        (cardWidth + spacing))
                                    .floor();

                            final showCount = maxBooks + 1;
                            return trendingBooks.length > showCount
                                ? showCount
                                : trendingBooks.length;
                          }(),
                          itemBuilder: (context, index) {
                            final book = trendingBooks[index];
                            return SmallBookCard(
                              title: book['title'] ?? 'Untitled',
                              authorName: book['author_name'] ?? 'Anonymous',
                              coverImageUrl: book['cover_image_url'] ?? '',
                              views: book['views'] ?? 0,
                              likes: book['likes'] ?? 0,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => BookDetailPage(
                                      bookId: book['id'],
                                      title: book['title'],
                                      authorName: book['author_name'],
                                      coverImageUrl: book['cover_image_url'],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Categories Section
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SectionHeader(title: 'Kategori Unggulan'),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          childAspectRatio: 1.5,
                          children: [
                            CategoryCard(
                              title: 'Fiksi',
                              icon: Icons.auto_stories,
                              color: Theme.of(context).colorScheme.primary,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => CategoryPage(
                                      category: 'Fiction',
                                      title: 'Fiksi',
                                      icon: Icons.auto_stories,
                                      color: Theme.of(
                                        context,
                                      ).colorScheme.primary,
                                    ),
                                  ),
                                );
                              },
                            ),
                            CategoryCard(
                              title: 'Non-Fiksi',
                              icon: Icons.menu_book,
                              color: Theme.of(context).colorScheme.secondary,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => CategoryPage(
                                      category: 'Non-Fiction',
                                      title: 'Non-Fiksi',
                                      icon: Icons.menu_book,
                                      color: Theme.of(
                                        context,
                                      ).colorScheme.secondary,
                                    ),
                                  ),
                                );
                              },
                            ),
                            CategoryCard(
                              title: 'Fantasi',
                              icon: Icons.psychology,
                              color: Colors.purple,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const CategoryPage(
                                      category: 'Fantasy',
                                      title: 'Fantasi',
                                      icon: Icons.psychology,
                                      color: Colors.purple,
                                    ),
                                  ),
                                );
                              },
                            ),
                            CategoryCard(
                              title: 'Sains Fiksi',
                              icon: Icons.rocket_launch,
                              color: Colors.blue,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const CategoryPage(
                                      category: 'Science Fiction',
                                      title: 'Sains Fiksi',
                                      icon: Icons.rocket_launch,
                                      color: Colors.blue,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),

                // Latest Books Section
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SectionHeader(
                        title: 'Buku Terbaru',
                        onSeeAll: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const BooksListPage(
                                title: 'Latest Books',
                                filterType: 'latest',
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(
                        height: 240,
                        child: ListView.builder(
                          padding: const EdgeInsets.only(left: 16),
                          scrollDirection: Axis.horizontal,
                          clipBehavior: Clip.none,
                          itemCount: () {
                            final screenWidth = MediaQuery.of(
                              context,
                            ).size.width;
                            const cardWidth = 120.0;
                            const padding = 16.0;
                            const spacing = 16.0;

                            final availableWidth = screenWidth - padding;
                            final maxBooks =
                                ((availableWidth + spacing) /
                                        (cardWidth + spacing))
                                    .floor();

                            final showCount = maxBooks + 1;
                            return books.length > showCount
                                ? showCount
                                : books.length;
                          }(),
                          itemBuilder: (context, index) {
                            final book = books[index];
                            return SmallBookCard(
                              title: book['title'] ?? 'Untitled',
                              authorName: book['author_name'] ?? 'Anonymous',
                              coverImageUrl: book['cover_image_url'] ?? '',
                              views: book['views'] ?? 0,
                              likes: book['likes'] ?? 0,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => BookDetailPage(
                                      bookId: book['id'],
                                      title: book['title'],
                                      authorName: book['author_name'],
                                      coverImageUrl: book['cover_image_url'],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
