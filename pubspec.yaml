name: whatthebook
description: "A Wattpad-like book reading and writing platform"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  printing: ^5.11.1
  cupertino_icons: ^1.0.8
  
  # State Management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.5

  # Dependency Injection
  get_it: ^8.0.3

  # Database & Storage
  supabase_flutter: ^2.3.4
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication
  # firebase_core: ^2.24.2
  # firebase_auth: ^4.15.3
  # firebase_auth_web: ^5.8.12
  # google_sign_in: ^6.1.6
  
  # Utils
  dartz: ^0.10.1
  json_annotation: ^4.8.1
  freezed_annotation: ^3.0.0
  image_picker: ^1.0.7
  share_plus: ^11.0.0
  file_picker: ^10.2.0
  flutter_markdown: ^0.7.7+1
  shared_preferences: ^2.2.2
  pdf: ^3.10.7
  path_provider: ^2.1.2
  universal_html: ^2.2.4

  # Rich Text Editor
  flutter_quill: ^11.4.1
  flutter_quill_extensions: ^11.0.0

  # Time formatting
  timeago: ^3.6.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  
  # Code Generation
  build_runner: ^2.4.8
  json_serializable: ^6.7.1
  freezed: ^3.0.0-0.0.dev
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
