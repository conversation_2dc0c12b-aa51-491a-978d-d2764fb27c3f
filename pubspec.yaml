name: whatthebook
description: "A Wattpad-like book reading and writing platform"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  printing: ^5.11.1
  cupertino_icons: ^1.0.8
  
  # State Management
  flutter_bloc: ^8.1.4
  equatable: ^2.0.5

  # Dependency Injection
  get_it: ^7.6.7

  # Database & Storage
  supabase_flutter: ^2.3.4
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication
  # firebase_core: ^2.24.2
  # firebase_auth: ^4.15.3
  # firebase_auth_web: ^5.8.12
  # google_sign_in: ^6.1.6
  
  # Utils
  dartz: ^0.10.1
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.1
  image_picker: ^1.0.7
  share_plus: ^7.2.2
  file_picker: ^8.3.7
  flutter_markdown: ^0.6.18
  shared_preferences: ^2.2.2
  pdf: ^3.10.7
  path_provider: ^2.1.2
  universal_html: ^2.2.4

  # Rich Text Editor
  flutter_quill: ^10.8.5
  flutter_quill_extensions: ^10.8.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  
  # Code Generation
  build_runner: ^2.4.8
  json_serializable: ^6.7.1
  freezed: ^2.4.7
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
