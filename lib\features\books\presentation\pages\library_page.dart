import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../widgets/library_book_card.dart';

class LibraryPage extends StatefulWidget {
  const LibraryPage({super.key});

  @override
  State<LibraryPage> createState() => _LibraryPageState();
}

class _LibraryPageState extends State<LibraryPage> {
  late Future<Map<String, dynamic>> _future;

  @override
  void initState() {
    super.initState();
    _future = _loadLibraryData();
  }

  Future<Map<String, dynamic>> _loadLibraryData() async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not logged in');
      }

      // Get all books
      final books = await Supabase.instance.client.from('books').select();

      // Get bookmarked books from bookmarks table
      final bookmarksData = await Supabase.instance.client
          .from('bookmarks')
          .select('book_id')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final bookmarkedBooks = books.where((book) {
        return bookmarksData.any(
          (bookmark) => bookmark['book_id'] == book['id'],
        );
      }).toList();

      // Get last read books from reading_progress table
      final readingProgressData = await Supabase.instance.client
          .from('reading_progress')
          .select()
          .eq('user_id', userId)
          .order('last_read_at', ascending: false);

      final lastReadBooks = books.where((book) {
        return readingProgressData.any(
          (progress) =>
              progress['book_id'] == book['id'] &&
              progress['last_read_at'] != null,
        );
      }).toList();

      // Sort last read books by last_read_at from reading_progress
      lastReadBooks.sort((a, b) {
        final aProgress = readingProgressData.firstWhere(
          (progress) => progress['book_id'] == a['id'],
        );
        final bProgress = readingProgressData.firstWhere(
          (progress) => progress['book_id'] == b['id'],
        );
        return DateTime.parse(
          bProgress['last_read_at'],
        ).compareTo(DateTime.parse(aProgress['last_read_at']));
      });

      return {
        'books': books,
        'bookmarked': bookmarkedBooks,
        'lastRead': lastReadBooks,
      };
    } catch (e) {
      debugPrint('Error loading library data: $e');
      return {'books': [], 'bookmarked': [], 'lastRead': []};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(
        0xFFF5F5DC,
      ), // Beige background like old library
      appBar: AppBar(
        title: const Text('Perpustakaan Saya'),
        backgroundColor: const Color(0xFF8B4513), // Saddle brown
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _future = _loadLibraryData();
              });
            },
          ),
        ],
      ),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _future,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(color: Color(0xFF8B4513)),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${snapshot.error}',
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _future = _loadLibraryData();
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF8B4513),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Coba Lagi'),
                  ),
                ],
              ),
            );
          }

          final data =
              snapshot.data ?? {'books': [], 'bookmarked': [], 'lastRead': []};
          final books = List<Map<String, dynamic>>.from(data['books']);
          final bookmarkedBooks = List<Map<String, dynamic>>.from(
            data['bookmarked'],
          );
          final lastReadBooks = List<Map<String, dynamic>>.from(
            data['lastRead'],
          );

          if (books.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.auto_stories, size: 80, color: Colors.brown[300]),
                  const SizedBox(height: 24),
                  Text(
                    'Rak Buku Kosong',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.brown[700],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Mulai koleksi buku Anda\nuntuk mengisi rak perpustakaan',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16, color: Colors.brown[500]),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              setState(() {
                _future = _loadLibraryData();
              });
            },
            color: const Color(0xFF8B4513),
            child: CustomScrollView(
              slivers: [
                // Bookshelf Header
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.library_books,
                          color: Colors.brown[700],
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          '${books.length} Buku dalam Koleksi',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.brown[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Books Horizontal Scroll
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 240,
                    child: ListView.builder(
                      padding: const EdgeInsets.only(left: 16),
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      clipBehavior: Clip.none,
                      itemCount: () {
                        // Calculate max books that can fit + 1 for peek effect
                        final screenWidth = MediaQuery.of(context).size.width;
                        const cardWidth = 120.0; // Fixed card width
                        const padding = 16.0; // Left padding only
                        const spacing = 12.0; // Margin between cards

                        final availableWidth = screenWidth - padding;
                        final maxBooks =
                            ((availableWidth + spacing) / (cardWidth + spacing))
                                .floor();

                        // Show max books that fit + 1 for peek effect, but not more than total books
                        final showCount = maxBooks + 1;
                        return books.length > showCount
                            ? showCount
                            : books.length;
                      }(),
                      itemBuilder: (context, index) {
                        final book = books[index];
                        return LibraryBookCard(book: book);
                      },
                    ),
                  ),
                ),

                // Last Read Section
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(Icons.history, color: Colors.brown[700], size: 28),
                        const SizedBox(width: 12),
                        Text(
                          'Terakhir Dibaca',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.brown[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 240,
                    child: lastReadBooks.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.history,
                                  size: 48,
                                  color: Colors.brown[300],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Belum ada buku yang dibaca',
                                  style: TextStyle(color: Colors.brown[500]),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.only(left: 16),
                            scrollDirection: Axis.horizontal,
                            physics: const BouncingScrollPhysics(),
                            clipBehavior: Clip.none,
                            itemCount: () {
                              final screenWidth = MediaQuery.of(
                                context,
                              ).size.width;
                              const cardWidth = 120.0;
                              const padding = 16.0;
                              const spacing = 12.0;

                              final availableWidth = screenWidth - padding;
                              final maxBooks =
                                  ((availableWidth + spacing) /
                                          (cardWidth + spacing))
                                      .floor();

                              final showCount = maxBooks + 1;
                              return lastReadBooks.length > showCount
                                  ? showCount
                                  : lastReadBooks.length;
                            }(),
                            itemBuilder: (context, index) {
                              final book = lastReadBooks[index];
                              return LibraryBookCard(book: book);
                            },
                          ),
                  ),
                ),

                // Bookmarked Section
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.bookmark,
                          color: Colors.brown[700],
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Buku Tersimpan',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.brown[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 240,
                    child: bookmarkedBooks.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.bookmark_outline,
                                  size: 48,
                                  color: Colors.brown[300],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Belum ada buku yang disimpan',
                                  style: TextStyle(color: Colors.brown[500]),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.only(left: 16),
                            scrollDirection: Axis.horizontal,
                            physics: const BouncingScrollPhysics(),
                            clipBehavior: Clip.none,
                            itemCount: () {
                              final screenWidth = MediaQuery.of(
                                context,
                              ).size.width;
                              const cardWidth = 120.0;
                              const padding = 16.0;
                              const spacing = 12.0;

                              final availableWidth = screenWidth - padding;
                              final maxBooks =
                                  ((availableWidth + spacing) /
                                          (cardWidth + spacing))
                                      .floor();

                              final showCount = maxBooks + 1;
                              return bookmarkedBooks.length > showCount
                                  ? showCount
                                  : bookmarkedBooks.length;
                            }(),
                            itemBuilder: (context, index) {
                              final book = bookmarkedBooks[index];
                              return LibraryBookCard(book: book);
                            },
                          ),
                  ),
                ),

                // Category Index Section
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.category,
                          color: Colors.brown[700],
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Rak Buku Berdasarkan Kategori',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.brown[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      childAspectRatio: 1.5,
                      children: [
                        _buildCategoryCard(
                          'Fiksi',
                          Icons.auto_stories,
                          Colors.blue,
                        ),
                        _buildCategoryCard(
                          'Non-Fiksi',
                          Icons.menu_book,
                          Colors.green,
                        ),
                        _buildCategoryCard(
                          'Fantasi',
                          Icons.psychology,
                          Colors.purple,
                        ),
                        _buildCategoryCard(
                          'Sains Fiksi',
                          Icons.rocket_launch,
                          Colors.orange,
                        ),
                        _buildCategoryCard(
                          'Romance',
                          Icons.favorite,
                          Colors.pink,
                        ),
                        _buildCategoryCard(
                          'Misteri',
                          Icons.search,
                          Colors.indigo,
                        ),
                      ],
                    ),
                  ),
                ),

                // Bottom spacing
                const SliverToBoxAdapter(child: SizedBox(height: 32)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategoryCard(String title, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: InkWell(
        onTap: () {
          // Navigate to category page
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Navigating to $title category')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
