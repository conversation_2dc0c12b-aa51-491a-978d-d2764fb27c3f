import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:convert';
import 'dart:math' as math;
import '../../widgets/editor/rich_text_editor.dart';
import '../../bloc/chapter_bloc.dart';
import '../../bloc/chapter_event.dart';
import '../../bloc/chapter_state.dart';
import '../../../domain/entities/chapter.dart';

class ChapterEditorPage extends StatefulWidget {
  final String bookId;

  const ChapterEditorPage({super.key, required this.bookId});

  @override
  State<ChapterEditorPage> createState() => _ChapterEditorPageState();
}

class _ChapterEditorPageState extends State<ChapterEditorPage> {
  final _titleController = TextEditingController();
  List<Chapter> _chapters = [];
  int _currentChapterIndex = 0;
  bool _isLoading = true;
  String _currentContent = '';
  int _currentWordCount = 0;
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _loadChapters();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _loadChapters() async {
    try {
      final response = await Supabase.instance.client
          .from('chapters')
          .select()
          .eq('book_id', widget.bookId)
          .order('chapter_number');

      setState(() {
        _chapters = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      if (_chapters.isEmpty) {
        _createNewChapter();
      } else {
        _loadChapter(_currentChapterIndex);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading chapters: $e')));
    }
  }

  void _loadChapter(int index) {
    if (index >= 0 && index < _chapters.length) {
      final chapter = _chapters[index];
      _titleController.text = chapter['title'] ?? '';

      // Load content into editor
      final content = chapter['content'] ?? '';
      List<EditorBlock> blocks = [];

      if (content.isNotEmpty) {
        try {
          // Try to parse as JSON
          final List<dynamic> jsonData = jsonDecode(content);
          blocks = jsonData
              .map((block) => EditorBlock.fromJson(block))
              .toList();
        } catch (e) {
          // If not JSON, treat as plain text and convert to blocks
          blocks = [
            EditorBlock(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              type: BlockType.text,
              content: content,
            ),
          ];
        }
      }

      setState(() {
        _currentBlocks = blocks;
        _currentChapterIndex = index;
      });
    }
  }

  Future<void> _saveCurrentChapter() async {
    if (_chapters.isEmpty) return;

    try {
      // Convert blocks to JSON
      final content = jsonEncode(
        _currentBlocks.map((block) => block.toJson()).toList(),
      );

      // Calculate word count
      final plainText = _currentBlocks.map((block) => block.content).join('\n');
      final wordCount = plainText
          .split(RegExp(r'\s+'))
          .where((word) => word.isNotEmpty)
          .length;

      final chapter = _chapters[_currentChapterIndex];
      await Supabase.instance.client
          .from('chapters')
          .update({
            'title': _titleController.text,
            'content': content,
            'word_count': wordCount,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', chapter['id']);

      // Update local data
      setState(() {
        _chapters[_currentChapterIndex]['title'] = _titleController.text;
        _chapters[_currentChapterIndex]['content'] = content;
        _chapters[_currentChapterIndex]['word_count'] = wordCount;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Chapter saved!')));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error saving chapter: $e')));
    }
  }

  Future<void> _createNewChapter() async {
    try {
      final chapterNumber = _chapters.length + 1;
      final response = await Supabase.instance.client.from('chapters').insert({
        'book_id': widget.bookId,
        'chapter_number': chapterNumber,
        'title': 'Chapter $chapterNumber',
        'content': '[]', // Empty array of blocks
        'word_count': 0,
        'status': 'draft',
      }).select();

      if (response.isNotEmpty) {
        setState(() {
          _chapters.add(response[0]);
          _currentChapterIndex = _chapters.length - 1;
        });
        _loadChapter(_currentChapterIndex);
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error creating chapter: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: _chapters.isNotEmpty
            ? SizedBox(
                height: 40,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _chapters.length,
                  itemBuilder: (context, index) {
                    final isSelected = index == _currentChapterIndex;
                    return GestureDetector(
                      onTap: () {
                        _saveCurrentChapter().then((_) {
                          _loadChapter(index);
                        });
                      },
                      child: Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          _chapters[index]['title'] ?? 'Chapter ${index + 1}',
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              )
            : const Text('New Chapter'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _chapters.isNotEmpty
                ? () async {
                    final chapter = _chapters[_currentChapterIndex];
                    final content = _currentBlocks
                        .map((block) => block.content)
                        .join('\n');
                    await Share.share(
                      'Check out this chapter: ${chapter['title']}\n\n${content.substring(0, math.min(100, content.length))}...',
                      subject: chapter['title'],
                    );
                  }
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: _chapters.isNotEmpty
                ? () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) => Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ListTile(
                            leading: const Icon(Icons.add),
                            title: const Text('New Chapter'),
                            onTap: () {
                              Navigator.pop(context);
                              _saveCurrentChapter().then((_) {
                                _createNewChapter();
                              });
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.info),
                            title: const Text('Chapter Info'),
                            onTap: () {
                              Navigator.pop(context);
                              final chapter = _chapters[_currentChapterIndex];
                              final wordCount = _currentBlocks
                                  .map((block) => block.content)
                                  .join(' ')
                                  .split(RegExp(r'\s+'))
                                  .where((word) => word.isNotEmpty)
                                  .length;
                              if (!mounted) return;
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Chapter Information'),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Current Word Count: $wordCount'),
                                      Text(
                                        'Saved Word Count: ${chapter['word_count'] ?? 0}',
                                      ),
                                      Text(
                                        'Status: ${chapter['status'] ?? 'draft'}',
                                      ),
                                      Text(
                                        'Created: ${DateTime.parse(chapter['created_at']).toLocal()}',
                                      ),
                                      Text(
                                        'Last Updated: ${DateTime.parse(chapter['updated_at']).toLocal()}',
                                      ),
                                    ],
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: const Text('Close'),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.delete),
                            title: const Text('Delete Chapter'),
                            onTap: () {
                              Navigator.pop(context);
                              if (_chapters.isEmpty) return;

                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Delete Chapter'),
                                  content: const Text(
                                    'Are you sure you want to delete this chapter? This action cannot be undone.',
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: const Text('Cancel'),
                                    ),
                                    TextButton(
                                      onPressed: () async {
                                        final navigator = Navigator.of(context);
                                        final scaffoldMessenger =
                                            ScaffoldMessenger.of(context);
                                        navigator.pop();
                                        try {
                                          final chapter =
                                              _chapters[_currentChapterIndex];
                                          await Supabase.instance.client
                                              .from('chapters')
                                              .delete()
                                              .eq('id', chapter['id']);

                                          if (!mounted) return;
                                          setState(() {
                                            _chapters.removeAt(
                                              _currentChapterIndex,
                                            );
                                            if (_currentChapterIndex >=
                                                _chapters.length) {
                                              _currentChapterIndex =
                                                  _chapters.length - 1;
                                            }
                                            if (_chapters.isNotEmpty) {
                                              _loadChapter(
                                                _currentChapterIndex,
                                              );
                                            } else {
                                              _titleController.clear();
                                              _currentBlocks = [];
                                            }
                                          });
                                        } catch (e) {
                                          if (!mounted) return;
                                          scaffoldMessenger.showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Error deleting chapter: $e',
                                              ),
                                            ),
                                          );
                                        }
                                      },
                                      style: TextButton.styleFrom(
                                        foregroundColor: Colors.red,
                                      ),
                                      child: const Text('Delete'),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  }
                : null,
          ),
        ],
      ),
      body: Column(
        children: [
          // Chapter title
          if (_chapters.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _titleController,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                decoration: const InputDecoration(
                  hintText: 'Chapter Title',
                  border: InputBorder.none,
                ),
              ),
            ),

          // Editor
          Expanded(
            child: NotionEditor(
              initialBlocks: _currentBlocks,
              onChanged: (blocks) {
                setState(() {
                  _currentBlocks = blocks;
                });
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "save",
        onPressed: _saveCurrentChapter,
        label: const Text('Save Chapter'),
        icon: const Icon(Icons.save),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }
}
