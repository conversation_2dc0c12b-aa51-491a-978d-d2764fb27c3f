import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://your-project-url.supabase.co',
    anon<PERSON>ey: 'your-anon-key',
  );

  // Test connection
  try {
    // Test reading_progress table
    print('Testing reading_progress table...');
    final progressResponse = await Supabase.instance.client
        .from('reading_progress')
        .select('*')
        .limit(1);
    print('Reading progress table exists: ${progressResponse.length}');

    // Test bookmarks table
    print('Testing bookmarks table...');
    final bookmarksResponse = await Supabase.instance.client
        .from('bookmarks')
        .select('*')
        .limit(1);
    print('Bookmarks table exists: ${bookmarksResponse.length}');

    // Test insert to reading_progress
    print('Testing insert to reading_progress...');
    final testUserId = 'test-user-id';
    final testBookId = 'test-book-id';
    final testChapterId = 'test-chapter-id';
    
    final insertResponse = await Supabase.instance.client
        .from('reading_progress')
        .upsert({
          'user_id': testUserId,
          'book_id': testBookId,
          'chapter_id': testChapterId,
          'progress_percentage': 50.0,
          'last_read_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        }, onConflict: 'user_id,book_id')
        .select();
    
    print('Insert successful: $insertResponse');

    // Test insert to bookmarks
    print('Testing insert to bookmarks...');
    final bookmarkResponse = await Supabase.instance.client
        .from('bookmarks')
        .upsert({
          'user_id': testUserId,
          'book_id': testBookId,
        }, onConflict: 'user_id,book_id')
        .select();
    
    print('Bookmark insert successful: $bookmarkResponse');

    // Clean up test data
    print('Cleaning up test data...');
    await Supabase.instance.client
        .from('reading_progress')
        .delete()
        .eq('user_id', testUserId)
        .eq('book_id', testBookId);
        
    await Supabase.instance.client
        .from('bookmarks')
        .delete()
        .eq('user_id', testUserId)
        .eq('book_id', testBookId);
    
    print('Test completed successfully!');
    
  } catch (e) {
    print('Error during test: $e');
  }
}
