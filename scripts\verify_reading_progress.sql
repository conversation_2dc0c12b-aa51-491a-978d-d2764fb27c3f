-- Verify reading progress data
-- Run this in Supabase SQL Editor

-- Check reading progress for all users (latest data)
SELECT
  rp.*,
  b.title as book_title,
  c.title as chapter_title,
  u.email as user_email
FROM public.reading_progress rp
LEFT JOIN public.books b ON rp.book_id = b.id
LEFT JOIN public.chapters c ON rp.chapter_id = c.id
LEFT JOIN public.users u ON rp.user_id = u.id
ORDER BY rp.updated_at DESC
LIMIT 10;

-- Check latest reading progress record
SELECT
  'Latest reading progress found!' as status,
  user_id,
  progress_percentage,
  last_read_at,
  created_at,
  updated_at
FROM public.reading_progress
WHERE id = 'abf492e2-d03a-43af-9460-50b1785426dd';

-- Check bookmarks for the latest user
SELECT
  b.*,
  bk.title as book_title,
  u.email as user_email
FROM public.bookmarks b
LEFT JOIN public.books bk ON b.book_id = bk.id
LEFT JOIN public.users u ON b.user_id = u.id
WHERE b.user_id = 'a0cbb05e-02d6-4d7b-976e-3e35e768f6ac';

-- Check user profile for latest user
SELECT
  'User profile status' as check_type,
  id,
  email,
  display_name,
  created_at
FROM public.users
WHERE id = 'a0cbb05e-02d6-4d7b-976e-3e35e768f6ac';

-- Summary of all data
SELECT
  'Summary' as type,
  (SELECT COUNT(*) FROM public.reading_progress) as total_reading_progress,
  (SELECT COUNT(*) FROM public.bookmarks) as total_bookmarks,
  (SELECT COUNT(*) FROM public.users) as total_users;
