-- Verify reading progress data
-- Run this in Supabase SQL Editor

-- Check reading progress for the user
SELECT 
  rp.*,
  b.title as book_title,
  c.title as chapter_title
FROM public.reading_progress rp
LEFT JOIN public.books b ON rp.book_id = b.id
LEFT JOIN public.chapters c ON rp.chapter_id = c.id
WHERE rp.user_id = 'ff6b6089-3acb-4a42-8765-0efd56837dfd'
ORDER BY rp.updated_at DESC;

-- Check if the specific record exists
SELECT 
  'Reading progress found!' as status,
  progress_percentage,
  last_read_at,
  created_at,
  updated_at
FROM public.reading_progress 
WHERE id = 'b949a74a-4955-4acf-bcfd-07e4611b3489';

-- Check bookmarks for the user
SELECT 
  b.*,
  bk.title as book_title
FROM public.bookmarks b
LEFT JOIN public.books bk ON b.book_id = bk.id
WHERE b.user_id = 'ff6b6089-3acb-4a42-8765-0efd56837dfd';

-- Check user profile
SELECT 
  'User profile status' as check_type,
  id,
  email,
  display_name,
  created_at
FROM public.users 
WHERE id = 'ff6b6089-3acb-4a42-8765-0efd56837dfd';
