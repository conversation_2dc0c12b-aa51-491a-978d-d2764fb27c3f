import 'package:flutter/material.dart';
import 'editor_block.dart';

class BlockWidget extends StatefulWidget {
  final EditorBlock block;
  final Function(EditorBlock) onChanged;
  final Function(String blockId) onDelete;
  final Function(String blockId, BlockType newType) onTypeChanged;
  final Function(String blockId) onNewBlockAfter;
  final Function(String blockId, bool hasFocus, bool hasSelection) onFocusChanged;
  final VoidCallback? onFocusNext;
  final VoidCallback? onFocusPrevious;
  final bool isSelected;

  const BlockWidget({
    super.key,
    required this.block,
    required this.onChanged,
    required this.onDelete,
    required this.onTypeChanged,
    required this.onNewBlockAfter,
    required this.onFocusChanged,
    this.onFocusNext,
    this.onFocusPrevious,
    this.isSelected = false,
  });

  @override
  State<BlockWidget> createState() => _BlockWidgetState();
}

class _BlockWidgetState extends State<BlockWidget> with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  bool _showTypeSelector = false;
  bool _hasFocus = false;
  bool _hasSelection = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.block.content);
    _focusNode = FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _controller.addListener(() {
      widget.block.content = _controller.text;
      widget.onChanged(widget.block);
      _checkSelection();
    });

    _focusNode.addListener(() {
      setState(() {
        _hasFocus = _focusNode.hasFocus;
      });
      _checkSelection();
      widget.onFocusChanged(widget.block.id, _hasFocus, _hasSelection);
      
      if (_hasFocus) {
        _animationController.forward();
      } else {
        _animationController.reverse();
        setState(() {
          _showTypeSelector = false;
        });
      }
    });
  }

  void _checkSelection() {
    final selection = _controller.selection;
    final hasSelection = selection.isValid && !selection.isCollapsed;
    if (hasSelection != _hasSelection) {
      setState(() {
        _hasSelection = hasSelection;
      });
      widget.onFocusChanged(widget.block.id, _hasFocus, _hasSelection);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Block type indicator
          _buildBlockTypeIndicator(),
          const SizedBox(width: 12),
          // Content area
          Expanded(
            child: _buildContentField(),
          ),
        ],
      ),
    );
  }

  Widget _buildBlockTypeIndicator() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: 32,
      height: 32,
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        color: _hasFocus 
            ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(6),
        border: _hasFocus 
            ? Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3))
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(6),
          onTap: () {
            setState(() {
              _showTypeSelector = !_showTypeSelector;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(6),
            child: Icon(
              _getBlockIcon(),
              size: 18,
              color: _hasFocus 
                  ? Theme.of(context).primaryColor
                  : Colors.grey.shade500,
            ),
          ),
        ),
      ),
    );
  }

  IconData _getBlockIcon() {
    switch (widget.block.type) {
      case BlockType.heading1:
        return Icons.looks_one_outlined;
      case BlockType.heading2:
        return Icons.looks_two_outlined;
      case BlockType.heading3:
        return Icons.looks_3_outlined;
      case BlockType.bulletList:
        return Icons.radio_button_unchecked;
      case BlockType.numberedList:
        return Icons.format_list_numbered_outlined;
      case BlockType.quote:
        return Icons.format_quote_outlined;
      case BlockType.image:
        return Icons.image_outlined;
      default:
        return Icons.drag_indicator;
    }
  }

  Widget _buildContentField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_showTypeSelector) _buildTypeSelector(),
        _buildTextField(),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: Text(
                  'Turn into',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
              const Divider(height: 8),
              _buildTypeOption('Text', BlockType.text, Icons.text_fields, 'Plain text'),
              _buildTypeOption('Heading 1', BlockType.heading1, Icons.looks_one_outlined, 'Big section heading'),
              _buildTypeOption('Heading 2', BlockType.heading2, Icons.looks_two_outlined, 'Medium section heading'),
              _buildTypeOption('Heading 3', BlockType.heading3, Icons.looks_3_outlined, 'Small section heading'),
              _buildTypeOption('Bullet List', BlockType.bulletList, Icons.radio_button_unchecked, 'Create a simple bulleted list'),
              _buildTypeOption('Numbered List', BlockType.numberedList, Icons.format_list_numbered_outlined, 'Create a list with numbering'),
              _buildTypeOption('Quote', BlockType.quote, Icons.format_quote_outlined, 'Capture a quote'),
              _buildTypeOption('Image', BlockType.image, Icons.image_outlined, 'Upload or embed with link'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeOption(String label, BlockType type, IconData icon, String description) {
    final isSelected = widget.block.type == type;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          widget.onTypeChanged(widget.block.id, type);
          setState(() {
            _showTypeSelector = false;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.1) : null,
          ),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.2)
                      : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon, 
                  size: 16, 
                  color: isSelected 
                      ? Theme.of(context).primaryColor
                      : Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label, 
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected 
                            ? Theme.of(context).primaryColor
                            : Colors.black87,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: _hasFocus 
            ? Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3))
            : null,
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        style: _getTextStyle(),
        decoration: InputDecoration(
          hintText: _getHintText(),
          hintStyle: TextStyle(
            color: Colors.grey.shade400,
            fontWeight: FontWeight.normal,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        ),
        maxLines: widget.block.type == BlockType.quote ? null : 1,
        onSubmitted: (value) {
          if (value.isEmpty && widget.block.type != BlockType.text) {
            widget.onTypeChanged(widget.block.id, BlockType.text);
          } else {
            widget.onNewBlockAfter(widget.block.id);
          }
        },
        onChanged: (value) {
          if (value.startsWith('/') && value.length == 1) {
            setState(() {
              _showTypeSelector = true;
            });
          } else if (_showTypeSelector && !value.startsWith('/')) {
            setState(() {
              _showTypeSelector = false;
            });
          }
        },
        onTap: () {
          if (!_controller.text.startsWith('/')) {
            setState(() {
              _showTypeSelector = false;
            });
          }
        },
      ),
    );
  }

  TextStyle _getTextStyle() {
    const baseStyle = TextStyle(
      height: 1.5,
      color: Colors.black87,
    );

    switch (widget.block.type) {
      case BlockType.heading1:
        return baseStyle.copyWith(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          height: 1.2,
        );
      case BlockType.heading2:
        return baseStyle.copyWith(
          fontSize: 26,
          fontWeight: FontWeight.bold,
          height: 1.3,
        );
      case BlockType.heading3:
        return baseStyle.copyWith(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          height: 1.4,
        );
      case BlockType.quote:
        return baseStyle.copyWith(
          fontSize: 18,
          fontStyle: FontStyle.italic,
          color: Colors.grey.shade700,
        );
      case BlockType.bulletList:
      case BlockType.numberedList:
        return baseStyle.copyWith(fontSize: 16);
      default:
        return baseStyle.copyWith(fontSize: 16);
    }
  }

  String _getHintText() {
    switch (widget.block.type) {
      case BlockType.heading1:
        return 'Heading 1';
      case BlockType.heading2:
        return 'Heading 2';
      case BlockType.heading3:
        return 'Heading 3';
      case BlockType.quote:
        return 'Empty quote';
      case BlockType.bulletList:
        return 'List';
      case BlockType.numberedList:
        return 'List';
      case BlockType.image:
        return 'Add an image...';
      default:
        return 'Type \'/\' for commands';
    }
  }
}
