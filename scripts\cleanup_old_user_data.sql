-- Cleanup old user data that doesn't match current auth users
-- Run this in Supabase SQL Editor

-- Check data before deletion
SELECT 'Before cleanup - Reading Progress' as status, COUNT(*) as count
FROM public.reading_progress;

SELECT 'Before cleanup - Bookmarks' as status, COUNT(*) as count  
FROM public.bookmarks;

SELECT 'Before cleanup - Users' as status, COUNT(*) as count
FROM public.users;

-- Delete reading progress for old user (Lutfi AMB)
DELETE FROM public.reading_progress 
WHERE user_id = 'ff6b6089-3acb-4a42-8765-0efd56837dfd';

-- Delete bookmarks for old user (Lutfi AMB)
DELETE FROM public.bookmarks 
WHERE user_id = 'ff6b6089-3acb-4a42-8765-0efd56837dfd';

-- Delete user profile for old user (Lutfi AMB)
DELETE FROM public.users 
WHERE id = 'ff6b6089-3acb-4a42-8765-0efd56837dfd';

-- Check data after deletion
SELECT 'After cleanup - Reading Progress' as status, COUNT(*) as count
FROM public.reading_progress;

SELECT 'After cleanup - Bookmarks' as status, COUNT(*) as count
FROM public.bookmarks;

SELECT 'After cleanup - Users' as status, COUNT(*) as count
FROM public.users;

-- Verify remaining data is for correct user (Retas Lintas Batas)
SELECT 
  'Remaining reading progress' as type,
  rp.*,
  b.title as book_title,
  c.title as chapter_title
FROM public.reading_progress rp
LEFT JOIN public.books b ON rp.book_id = b.id
LEFT JOIN public.chapters c ON rp.chapter_id = c.id
WHERE rp.user_id = 'a0cbb05e-02d6-4d7b-976e-3e35e768f6ac';

-- Verify user profile
SELECT 
  'Remaining user profile' as type,
  *
FROM public.users 
WHERE id = 'a0cbb05e-02d6-4d7b-976e-3e35e768f6ac';
