import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/usecases/search_books.dart';
import '../models/book_model.dart';

abstract class BookRemoteDataSource {
  Future<List<BookModel>> getBooks({
    int page = 1,
    int limit = 20,
    String? genre,
    String? searchQuery,
  });

  Future<BookModel> getBookById(String bookId);

  Future<BookModel> createBook({
    required String title,
    required String description,
    required List<String> genres,
    String? coverImageUrl,
  });

  Future<BookModel> updateBook({
    required String bookId,
    String? title,
    String? description,
    List<String>? genres,
    String? coverImageUrl,
    bool? isCompleted,
    bool? isPublished,
  });

  Future<void> deleteBook(String bookId);

  Future<List<BookModel>> getMyBooks();

  Future<List<BookModel>> searchBooks({
    required String query,
    SearchFilters? filters,
    String? sortBy,
    int page = 1,
    int limit = 20,
  });
}

class BookRemoteDataSourceImpl implements BookRemoteDataSource {
  final SupabaseClient client;

  BookRemoteDataSourceImpl({required this.client});

  @override
  Future<List<BookModel>> getBooks({
    int page = 1,
    int limit = 20,
    String? genre,
    String? searchQuery,
  }) async {
    try {
      var query = client.from('books').select();

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.textSearch('title', searchQuery);
      }

      if (genre != null && genre.isNotEmpty) {
        query = query.contains('genres', [genre]);
      }

      final response = await query
          .eq('is_published', true)
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      return (response as List)
          .map((book) => BookModel.fromSupabase(book))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<BookModel> getBookById(String bookId) async {
    try {
      final response = await client
          .from('books')
          .select()
          .eq('id', bookId)
          .single();

      return BookModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<BookModel> createBook({
    required String title,
    required String description,
    required List<String> genres,
    String? coverImageUrl,
  }) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      final response = await client
          .from('books')
          .insert({
            'title': title,
            'description': description,
            'genres': genres,
            'cover_image_url': coverImageUrl ?? '',
            'author_id': userId,
            'author_name':
                client.auth.currentUser?.userMetadata?['full_name'] ??
                'Anonymous',
            'is_completed': false,
            'is_published': false,
            'total_chapters': 0,
            'views': 0,
            'likes': 0,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      return BookModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<BookModel> updateBook({
    required String bookId,
    String? title,
    String? description,
    List<String>? genres,
    String? coverImageUrl,
    bool? isCompleted,
    bool? isPublished,
  }) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      final Map<String, dynamic> updates = {};
      if (title != null) updates['title'] = title;
      if (description != null) updates['description'] = description;
      if (genres != null) updates['genres'] = genres;
      if (coverImageUrl != null) updates['cover_image_url'] = coverImageUrl;
      if (isCompleted != null) updates['is_completed'] = isCompleted;
      if (isPublished != null) updates['is_published'] = isPublished;
      updates['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('books')
          .update(updates)
          .eq('id', bookId)
          .eq('author_id', userId)
          .select()
          .single();

      return BookModel.fromSupabase(response);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<void> deleteBook(String bookId) async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      await client
          .from('books')
          .delete()
          .eq('id', bookId)
          .eq('author_id', userId);
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<List<BookModel>> getMyBooks() async {
    try {
      final userId = client.auth.currentUser?.id;
      if (userId == null) throw const ServerFailure('User not authenticated');

      final response = await client
          .from('books')
          .select()
          .eq('author_id', userId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((book) => BookModel.fromSupabase(book))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }

  @override
  Future<List<BookModel>> searchBooks({
    required String query,
    SearchFilters? filters,
    String? sortBy,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      var queryBuilder = client.from('books').select();

      // Apply text search
      if (query.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'title.ilike.%$query%,description.ilike.%$query%,author_name.ilike.%$query%',
        );
      }

      // Apply filters
      if (filters != null) {
        if (filters.genres != null && filters.genres!.isNotEmpty) {
          queryBuilder = queryBuilder.overlaps('genres', filters.genres!);
        }

        if (filters.author != null && filters.author!.isNotEmpty) {
          queryBuilder = queryBuilder.ilike(
            'author_name',
            '%${filters.author}%',
          );
        }

        if (filters.isCompleted != null) {
          queryBuilder = queryBuilder.eq('is_completed', filters.isCompleted!);
        }

        if (filters.isPublished != null) {
          queryBuilder = queryBuilder.eq('is_published', filters.isPublished!);
        }

        if (filters.dateRange != null) {
          if (filters.dateRange!.startDate != null) {
            queryBuilder = queryBuilder.gte(
              'created_at',
              filters.dateRange!.startDate!.toIso8601String(),
            );
          }
          if (filters.dateRange!.endDate != null) {
            queryBuilder = queryBuilder.lte(
              'created_at',
              filters.dateRange!.endDate!.toIso8601String(),
            );
          }
        }

        if (filters.minWordCount != null) {
          queryBuilder = queryBuilder.gte('total_words', filters.minWordCount!);
        }

        if (filters.maxWordCount != null) {
          queryBuilder = queryBuilder.lte('total_words', filters.maxWordCount!);
        }
      }

      // Apply sorting and pagination
      String orderColumn = 'created_at';
      bool ascending = false;

      switch (sortBy) {
        case 'title':
          orderColumn = 'title';
          ascending = true;
          break;
        case 'author':
          orderColumn = 'author_name';
          ascending = true;
          break;
        case 'date_asc':
          orderColumn = 'created_at';
          ascending = true;
          break;
        case 'date_desc':
          orderColumn = 'created_at';
          ascending = false;
          break;
        case 'popularity':
          orderColumn = 'views';
          ascending = false;
          break;
        case 'rating':
          orderColumn = 'likes';
          ascending = false;
          break;
        default:
          orderColumn = 'created_at';
          ascending = false;
      }

      final response = await queryBuilder
          .order(orderColumn, ascending: ascending)
          .range((page - 1) * limit, page * limit - 1);

      return (response as List)
          .map((book) => BookModel.fromSupabase(book))
          .toList();
    } catch (e) {
      throw ServerFailure(e.toString());
    }
  }
}
