import 'package:equatable/equatable.dart';

class Book extends Equatable {
  final String id;
  final String title;
  final String description;
  final String authorId;
  final String authorName;
  final String coverImageUrl;
  final List<String> genres;
  final int totalChapters;
  final bool isCompleted;
  final bool isPublished;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int views;
  final int likes;

  const Book({
    required this.id,
    required this.title,
    required this.description,
    required this.authorId,
    required this.authorName,
    required this.coverImageUrl,
    required this.genres,
    required this.totalChapters,
    required this.isCompleted,
    required this.isPublished,
    required this.createdAt,
    required this.updatedAt,
    required this.views,
    required this.likes,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        authorId,
        authorName,
        coverImageUrl,
        genres,
        totalChapters,
        isCompleted,
        isPublished,
        createdAt,
        updatedAt,
        views,
        likes,
      ];
}
