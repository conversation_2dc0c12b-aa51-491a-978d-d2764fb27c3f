import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../widgets/book_card.dart';
import 'book_detail_page.dart';

class BooksListPage extends StatefulWidget {
  final String title;
  final String? filterType;

  const BooksListPage({
    super.key,
    required this.title,
    this.filterType,
  });

  @override
  State<BooksListPage> createState() => _BooksListPageState();
}

class _BooksListPageState extends State<BooksListPage> {
  List<Map<String, dynamic>> books = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  Future<void> _loadBooks() async {
    try {
      late final List<Map<String, dynamic>> response;

      // Apply different filters based on the type
      switch (widget.filterType) {
        case 'recommended':
          // For recommended, we could order by likes or views
          response = await Supabase.instance.client
              .from('books')
              .select()
              .order('likes', ascending: false)
              .limit(20);
          break;
        case 'trending':
          // For trending, order by recent activity or views
          response = await Supabase.instance.client
              .from('books')
              .select()
              .order('views', ascending: false)
              .limit(20);
          break;
        case 'latest':
          // For latest, order by creation date
          response = await Supabase.instance.client
              .from('books')
              .select()
              .order('created_at', ascending: false)
              .limit(20);
          break;
        default:
          // Default: show all books
          response = await Supabase.instance.client
              .from('books')
              .select()
              .order('created_at', ascending: false);
          break;
      }

      setState(() {
        books = List<Map<String, dynamic>>.from(response);
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading books: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                isLoading = true;
              });
              _loadBooks();
            },
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : books.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.book,
                        size: 64,
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No books found',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Check back later for new books',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadBooks,
                  child: CustomScrollView(
                    slivers: [
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            '${books.length} book${books.length == 1 ? '' : 's'}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      SliverPadding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        sliver: SliverGrid(
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.6,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final book = books[index];
                              return BookCard(
                                title: book['title'] ?? 'Untitled',
                                description: book['description'] ?? 'No description',
                                authorName: book['author_name'] ?? 'Anonymous',
                                genres: List<String>.from(book['genres'] ?? []),
                                coverImageUrl: book['cover_image_url'] ?? '',
                                views: book['views'] ?? 0,
                                likes: book['likes'] ?? 0,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => BookDetailPage(
                                        bookId: book['id'],
                                        title: book['title'],
                                        authorName: book['author_name'],
                                        coverImageUrl: book['cover_image_url'],
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            childCount: books.length,
                          ),
                        ),
                      ),
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 24),
                      ),
                    ],
                  ),
                ),
    );
  }
}
