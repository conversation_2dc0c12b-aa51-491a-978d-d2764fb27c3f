-- Safe migration for chapters table - NO DATA LOSS
-- This script only adds missing columns and creates policies if they don't exist

-- Add missing columns if they don't exist (completely safe)
DO $$ 
BEGIN
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'chapters' AND column_name = 'status') THEN
        ALTER TABLE chapters ADD COLUMN status text not null default 'draft'::text;
    END IF;

    -- Add word_count column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'chapters' AND column_name = 'word_count') THEN
        ALTER TABLE chapters ADD COLUMN word_count integer null default 0;
    END IF;
END $$;

-- Create chapters table if it doesn't exist (safe - won't affect existing table)
CREATE TABLE IF NOT EXISTS public.chapters (
  id uuid not null default extensions.uuid_generate_v4(),
  book_id uuid null,
  title text not null,
  content text not null,
  chapter_number integer not null,
  word_count integer null default 0,
  status text not null default 'draft'::text,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint chapters_pkey primary key (id),
  constraint chapters_book_id_fkey foreign key (book_id) references books (id) on delete cascade
) tablespace pg_default;

-- Enable Row Level Security (safe to run multiple times)
ALTER TABLE public.chapters ENABLE ROW LEVEL SECURITY;

-- Create policies ONLY if they don't exist (completely safe - no dropping)
DO $$ 
BEGIN
    -- Only create policies if they don't already exist
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chapters' AND policyname = 'Allow public read access') THEN
        CREATE POLICY "Allow public read access" ON public.chapters
            FOR SELECT USING (true);
    END IF;
        
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chapters' AND policyname = 'Allow authenticated users to insert') THEN
        CREATE POLICY "Allow authenticated users to insert" ON public.chapters
            FOR INSERT WITH CHECK (true);
    END IF;
        
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chapters' AND policyname = 'Allow users to update their own chapters') THEN
        CREATE POLICY "Allow users to update their own chapters" ON public.chapters
            FOR UPDATE USING (true);
    END IF;
        
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'chapters' AND policyname = 'Allow users to delete their own chapters') THEN
        CREATE POLICY "Allow users to delete their own chapters" ON public.chapters
            FOR DELETE USING (true);
    END IF;
END $$;

-- Create indexes for better performance (safe to run multiple times)
CREATE INDEX IF NOT EXISTS idx_chapters_book_id ON public.chapters(book_id);
CREATE INDEX IF NOT EXISTS idx_chapters_book_chapter ON public.chapters(book_id, chapter_number);
