import 'book.dart';

class LibraryBook extends Book {
  final String? lastReadChapterId;
  final int? lastReadPage;
  final DateTime? lastReadAt;
  final double readingProgress; // Percentage of completion (0-100)

  const LibraryBook({
    required super.id,
    required super.title,
    required super.description,
    required super.authorId,
    required super.authorName,
    required super.coverImageUrl,
    required super.genres,
    required super.totalChapters,
    required super.isCompleted,
    required super.isPublished,
    required super.createdAt,
    required super.updatedAt,
    required super.views,
    required super.likes,
    this.lastReadChapterId,
    this.lastReadPage,
    this.lastReadAt,
    required this.readingProgress,
  });

  @override
  List<Object?> get props => [
    ...super.props,
    lastReadChapterId,
    lastReadPage,
    lastReadAt,
    readingProgress,
  ];

  LibraryBook copyWith({
    String? id,
    String? title,
    String? description,
    String? authorId,
    String? authorName,
    String? coverImageUrl,
    List<String>? genres,
    int? totalChapters,
    bool? isCompleted,
    bool? isPublished,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? views,
    int? likes,
    String? lastReadChapterId,
    int? lastReadPage,
    DateTime? lastReadAt,
    double? readingProgress,
  }) {
    return LibraryBook(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      genres: genres ?? this.genres,
      totalChapters: totalChapters ?? this.totalChapters,
      isCompleted: isCompleted ?? this.isCompleted,
      isPublished: isPublished ?? this.isPublished,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      views: views ?? this.views,
      likes: likes ?? this.likes,
      lastReadChapterId: lastReadChapterId ?? this.lastReadChapterId,
      lastReadPage: lastReadPage ?? this.lastReadPage,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      readingProgress: readingProgress ?? this.readingProgress,
    );
  }
}
