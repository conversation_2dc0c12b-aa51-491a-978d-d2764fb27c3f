-- Setup RLS policies for reading_progress table
-- Run this in Supabase SQL Editor

-- Enable RLS on reading_progress table
ALTER TABLE public.reading_progress ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own reading progress" ON public.reading_progress;
DROP POLICY IF EXISTS "Users can insert their own reading progress" ON public.reading_progress;
DROP POLICY IF EXISTS "Users can update their own reading progress" ON public.reading_progress;
DROP POLICY IF EXISTS "Users can delete their own reading progress" ON public.reading_progress;

-- Create policies for reading_progress table
CREATE POLICY "Users can view their own reading progress"
ON public.reading_progress
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reading progress"
ON public.reading_progress
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reading progress"
ON public.reading_progress
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reading progress"
ON public.reading_progress
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- Test the policies by checking if user can access their data
SELECT 
  'reading_progress policies created successfully' as status,
  count(*) as existing_records
FROM public.reading_progress 
WHERE user_id = auth.uid();

-- Also check if user exists in users table
SELECT 
  'user profile status' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM public.users WHERE id = '9fd14ae7-0e04-4d50-aa9c-8f64fb473318') 
    THEN 'User profile exists' 
    ELSE 'User profile missing' 
  END as status;
