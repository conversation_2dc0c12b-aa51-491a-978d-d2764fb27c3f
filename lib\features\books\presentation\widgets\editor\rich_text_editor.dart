import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';

class RichTextEditor extends StatefulWidget {
  final String? initialContent;
  final Function(String content, int wordCount)? onContentChanged;
  final Function(String content, int wordCount)? onAutoSave;
  final bool readOnly;

  const RichTextEditor({
    super.key,
    this.initialContent,
    this.onContentChanged,
    this.onAutoSave,
    this.readOnly = false,
  });

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  late QuillController _controller;
  Timer? _autoSaveTimer;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  void _initializeController() {
    Document document;

    if (widget.initialContent != null && widget.initialContent!.isNotEmpty) {
      try {
        // Try to parse as Quill Delta JSON
        final deltaJson = jsonDecode(widget.initialContent!);
        document = Document.fromJson(deltaJson);
      } catch (e) {
        // If not valid JSON, treat as plain text
        document = Document()..insert(0, widget.initialContent!);
      }
    } else {
      document = Document();
    }

    _controller = QuillController(
      document: document,
      selection: const TextSelection.collapsed(offset: 0),
    );

    if (!widget.readOnly) {
      _controller.addListener(_onContentChanged);
    }
  }

  void _onContentChanged() {
    final content = jsonEncode(_controller.document.toDelta().toJson());
    final plainText = _controller.document.toPlainText();
    final wordCount = _calculateWordCount(plainText);

    // Call immediate callback
    widget.onContentChanged?.call(content, wordCount);

    // Setup auto-save with debounce
    if (widget.onAutoSave != null) {
      _autoSaveTimer?.cancel();
      _autoSaveTimer = Timer(const Duration(seconds: 2), () {
        widget.onAutoSave?.call(content, wordCount);
      });
    }
  }

  int _calculateWordCount(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Toolbar
        if (!widget.readOnly) ...[
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: QuillSimpleToolbar(controller: _controller),
          ),
          const SizedBox(height: 8),
        ],

        // Editor
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: QuillEditor.basic(
              controller: _controller,
              focusNode: _focusNode,
            ),
          ),
        ),

        // Status bar
        if (!widget.readOnly)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Words: ${_calculateWordCount(_controller.document.toPlainText())}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                const Spacer(),
                Text(
                  'Auto-save enabled',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
