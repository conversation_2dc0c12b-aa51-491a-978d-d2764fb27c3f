import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../widgets/editor/editor_block.dart';
import '../../../../core/services/pdf_export_service.dart';

class BookReaderPage extends StatefulWidget {
  final String bookId;
  final String? initialChapterId;
  final String bookTitle;

  const BookReaderPage({
    super.key,
    required this.bookId,
    this.initialChapterId,
    required this.bookTitle,
  });

  @override
  State<BookReaderPage> createState() => _BookReaderPageState();
}

class _BookReaderPageState extends State<BookReaderPage>
    with TickerProviderStateMixin {
  // Data
  List<Map<String, dynamic>> chapters = [];
  int currentChapterIndex = 0;
  List<String> currentPages = [];
  int currentPageIndex = 0;
  bool isLoading = true;

  // Reading settings
  double fontSize = 16.0;
  double lineHeight = 1.5;
  bool isDarkMode = false;
  String fontFamily = 'Default';
  double pageMargin = 24.0;

  // UI Controllers
  late PageController pageController;
  late AnimationController settingsAnimationController;
  late AnimationController progressAnimationController;
  bool showSettings = false;
  bool showProgress = false;
  bool showAppBar = true;

  // Reading progress
  double readingProgress = 0.0;
  int totalPages = 0;
  int currentGlobalPage = 0;

  // Reading state
  Set<String> bookmarks = {};
  String? lastReadChapterId;
  int? lastReadPageIndex;

  // Search functionality
  bool showSearch = false;
  String searchQuery = '';
  List<Map<String, dynamic>> searchResults = [];
  TextEditingController searchController = TextEditingController();

  // Reading time estimation
  int estimatedReadingTimeMinutes = 0;

  @override
  void initState() {
    super.initState();
    pageController = PageController();
    settingsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _loadBook();
    _loadReadingSettings();
  }

  @override
  void dispose() {
    pageController.dispose();
    settingsAnimationController.dispose();
    progressAnimationController.dispose();
    searchController.dispose();
    super.dispose();
  }

  Future<void> _loadBook() async {
    try {
      // Load all chapters
      final response = await Supabase.instance.client
          .from('chapters')
          .select()
          .eq('book_id', widget.bookId)
          .order('chapter_number');

      setState(() {
        chapters = List<Map<String, dynamic>>.from(response);
      });

      // Find initial chapter
      if (widget.initialChapterId != null) {
        final index = chapters.indexWhere(
          (chapter) => chapter['id'] == widget.initialChapterId,
        );
        if (index != -1) {
          currentChapterIndex = index;
        }
      }

      await _loadCurrentChapter();
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading book: $e')));
    }
  }

  Future<void> _loadCurrentChapter() async {
    if (chapters.isEmpty) return;

    try {
      final chapter = chapters[currentChapterIndex];
      final content = chapter['content'] ?? '';

      // Parse content and split into pages
      final text = _parseContentToText(content);
      final pages = _splitTextIntoPages(text);

      setState(() {
        currentPages = pages;
        currentPageIndex = 0;
        isLoading = false;
      });

      _calculateProgress();
      _calculateReadingTime();
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  String _parseContentToText(String content) {
    if (content.isEmpty) return '';

    try {
      // Try to parse as JSON (editor blocks)
      final List<dynamic> blocks = jsonDecode(content);
      return blocks
          .map((block) => EditorBlock.fromJson(block).content)
          .join('\n\n');
    } catch (e) {
      // If not JSON, return as plain text
      return content;
    }
  }

  List<String> _splitTextIntoPages(String text) {
    if (text.isEmpty) return [''];

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.justify,
    );

    final style = TextStyle(
      fontSize: fontSize,
      height: lineHeight,
      fontFamily: fontFamily == 'Default' ? null : fontFamily,
    );

    // Calculate available space
    final screenSize = MediaQuery.of(context).size;
    final availableWidth = screenSize.width - (pageMargin * 2);
    final availableHeight =
        screenSize.height -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        MediaQuery.of(context).padding.bottom -
        (pageMargin * 2) -
        100; // Extra space for controls

    final pages = <String>[];
    final words = text.split(' ');
    String currentPage = '';

    for (int i = 0; i < words.length; i++) {
      final testPage = currentPage.isEmpty
          ? words[i]
          : '$currentPage ${words[i]}';

      textPainter.text = TextSpan(text: testPage, style: style);
      textPainter.layout(maxWidth: availableWidth);

      if (textPainter.height > availableHeight && currentPage.isNotEmpty) {
        pages.add(currentPage.trim());
        currentPage = words[i];
      } else {
        currentPage = testPage;
      }
    }

    if (currentPage.isNotEmpty) {
      pages.add(currentPage.trim());
    }

    return pages.isEmpty ? [''] : pages;
  }

  void _calculateProgress() {
    // Calculate total pages across all chapters
    totalPages = 0;
    currentGlobalPage = 0;

    for (int i = 0; i < chapters.length; i++) {
      final chapterContent = _parseContentToText(chapters[i]['content'] ?? '');
      final chapterPages = _splitTextIntoPages(chapterContent);

      if (i < currentChapterIndex) {
        currentGlobalPage += chapterPages.length;
      } else if (i == currentChapterIndex) {
        currentGlobalPage += currentPageIndex + 1;
      }

      totalPages += chapterPages.length;
    }

    readingProgress = totalPages > 0 ? currentGlobalPage / totalPages : 0.0;
  }

  void _calculateReadingTime() {
    // Average reading speed: 200-250 words per minute
    const averageWordsPerMinute = 225;

    int totalWords = 0;
    for (final chapter in chapters) {
      totalWords += (chapter['word_count'] as num?)?.toInt() ?? 0;
    }

    setState(() {
      estimatedReadingTimeMinutes = (totalWords / averageWordsPerMinute)
          .round();
    });
  }

  Future<void> _loadReadingSettings() async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;

      // Initialize with empty state
      setState(() {
        bookmarks = {};
        lastReadChapterId = null;
        lastReadPageIndex = null;
      });

      if (userId == null) {
        // If no user, try local storage
        final prefs = await SharedPreferences.getInstance();
        final localState = prefs.getString('reading_state_${widget.bookId}');

        if (localState != null) {
          final Map<String, dynamic> savedState = jsonDecode(localState);
          setState(() {
            bookmarks = Set<String>.from(savedState['bookmarks'] ?? []);
            lastReadChapterId = savedState['last_read_chapter'];
            lastReadPageIndex = savedState['last_read_page'];
          });
        }
        return;
      }

      try {
        // Load bookmarks from bookmarks table - fetch all bookmarks for user
        final bookmarksResponse = await Supabase.instance.client
            .from('bookmarks')
            .select('book_id')
            .eq('user_id', userId);

        // Load reading progress from reading_progress table
        final readingProgressResponse = await Supabase.instance.client
            .from('reading_progress')
            .select('chapter_id, progress_percentage, last_read_at')
            .eq('book_id', widget.bookId)
            .eq('user_id', userId)
            .maybeSingle();

        setState(() {
          if (bookmarksResponse.isNotEmpty) {
            bookmarks = bookmarksResponse
                .map<String>((item) => item['book_id'] as String)
                .toSet();
          }
          if (readingProgressResponse != null) {
            lastReadChapterId = readingProgressResponse['chapter_id'];
            // We'll calculate page index when loading the chapter
            lastReadPageIndex = 0; // Start from beginning of chapter
          }
        });
      } catch (e) {
        // If Supabase fails (offline or table missing), try local storage
        final prefs = await SharedPreferences.getInstance();
        final localState = prefs.getString('reading_state_${widget.bookId}');

        if (localState != null) {
          final Map<String, dynamic> savedState = jsonDecode(localState);
          setState(() {
            bookmarks = Set<String>.from(savedState['bookmarks'] ?? []);
            lastReadChapterId = savedState['last_read_chapter'];
            lastReadPageIndex = savedState['last_read_page'];
          });
        }
      }
    } catch (e) {
      debugPrint('Error in reading settings: $e');
    }
  }

  Future<void> saveReadingState() async {
    try {
      debugPrint('=== SAVE READING STATE START ===');
      debugPrint('Attempting to save reading state...');

      // Only attempt to save if we have chapters
      if (chapters.isEmpty) {
        debugPrint('No chapters to save');
        return;
      }

      final userId = Supabase.instance.client.auth.currentUser?.id;
      debugPrint('Current user ID: $userId');
      debugPrint('Current chapter index: $currentChapterIndex');
      debugPrint('Current page index: $currentPageIndex');
      debugPrint('Book ID: ${widget.bookId}');

      // Always save to local storage first as backup
      final prefs = await SharedPreferences.getInstance();
      final localState = {
        'book_id': widget.bookId,
        'bookmarks': bookmarks.toList(),
        'last_read_chapter': chapters[currentChapterIndex]['id'],
        'last_read_page': currentPageIndex,
        'last_read_at': DateTime.now().toIso8601String(),
      };
      await prefs.setString(
        'reading_state_${widget.bookId}',
        jsonEncode(localState),
      );
      debugPrint('Saved to local storage successfully');

      if (userId == null) {
        debugPrint('No user ID - only local storage saved');
        return;
      }

      final now = DateTime.now();
      final chapterId = chapters[currentChapterIndex]['id'];

      try {
        // Calculate progress percentage based on current page and total pages
        final progressPercentage = (currentGlobalPage / totalPages * 100).clamp(
          0,
          100,
        );

        debugPrint('Saving to Supabase...');

        // Try to save to Supabase (with error handling)
        try {
          // Check if user exists first
          final userExists = await _checkUserExists(userId);
          if (!userExists) {
            debugPrint(
              'User does not exist in users table, attempting to create profile...',
            );

            // Try to create user profile
            final profileCreated = await _createUserProfile(userId);
            if (!profileCreated) {
              debugPrint(
                'Failed to create user profile, skipping Supabase save',
              );
              return;
            }
            debugPrint(
              'User profile created successfully, proceeding with Supabase save',
            );
          }

          // Save reading progress
          debugPrint('Saving reading progress...');
          debugPrint('Chapter ID: $chapterId');
          debugPrint('Progress percentage: $progressPercentage');

          final progressData = {
            'user_id': userId,
            'book_id': widget.bookId,
            'chapter_id': chapterId,
            'progress_percentage': progressPercentage,
            'last_read_at': now.toIso8601String(),
            'updated_at': now.toIso8601String(),
          };
          debugPrint('Progress data to save: $progressData');

          final progressResponse = await Supabase.instance.client
              .from('reading_progress')
              .upsert(progressData, onConflict: 'user_id,book_id')
              .select();
          debugPrint('Reading progress saved successfully: $progressResponse');

          // Save bookmarks to bookmarks table (only if we have bookmarks)
          if (bookmarks.isNotEmpty) {
            debugPrint('Saving bookmarks...');
            try {
              // Upsert all bookmarks in bulk
              final bookmarksList = bookmarks
                  .map((bookId) => {'user_id': userId, 'book_id': bookId})
                  .toList();
              await Supabase.instance.client
                  .from('bookmarks')
                  .upsert(bookmarksList, onConflict: 'user_id,book_id');
              debugPrint('Bookmarks saved successfully');
            } catch (bookmarkError) {
              debugPrint('Bookmark save error (non-critical): $bookmarkError');
            }
          }
        } catch (supabaseError) {
          debugPrint('Supabase save failed: $supabaseError');
          // Local storage already saved above, so we're good
        }
      } catch (e) {
        debugPrint('Error saving to Supabase: $e');
        // If Supabase save fails (offline), save to local storage
        final prefs = await SharedPreferences.getInstance();
        final localState = {
          'book_id': widget.bookId,
          'bookmarks': bookmarks.toList(),
          'last_read_chapter': chapterId,
          'last_read_page': currentPageIndex,
          'last_read_at': now.toIso8601String(),
        };
        await prefs.setString(
          'reading_state_${widget.bookId}',
          jsonEncode(localState),
        );
      }
    } catch (e) {
      debugPrint('Error saving reading state: $e');
    }
  }

  void toggleBookmark() {
    final bookId = widget.bookId;
    setState(() {
      if (bookmarks.contains(bookId)) {
        bookmarks.remove(bookId);
      } else {
        bookmarks.add(bookId);
      }
    });

    // Save bookmark directly to Supabase
    _saveBookmark(bookId, bookmarks.contains(bookId));

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          bookmarks.contains(bookId) ? 'Bookmark added' : 'Bookmark removed',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  Future<void> _saveBookmark(String bookId, bool isBookmarked) async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('No user logged in, saving bookmark to local storage only');
        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        final localBookmarks = prefs.getStringList('bookmarks_$userId') ?? [];
        if (isBookmarked && !localBookmarks.contains(bookId)) {
          localBookmarks.add(bookId);
        } else if (!isBookmarked) {
          localBookmarks.remove(bookId);
        }
        await prefs.setStringList('bookmarks_$userId', localBookmarks);
        return;
      }

      // Check if user exists in users table
      final userExists = await _checkUserExists(userId);
      if (!userExists) {
        debugPrint(
          'User does not exist in users table, saving to local storage only',
        );
        // Save to local storage as fallback
        final prefs = await SharedPreferences.getInstance();
        final localBookmarks = prefs.getStringList('bookmarks_$userId') ?? [];
        if (isBookmarked && !localBookmarks.contains(bookId)) {
          localBookmarks.add(bookId);
        } else if (!isBookmarked) {
          localBookmarks.remove(bookId);
        }
        await prefs.setStringList('bookmarks_$userId', localBookmarks);
        return;
      }

      if (isBookmarked) {
        // Add bookmark
        await Supabase.instance.client.from('bookmarks').upsert({
          'user_id': userId,
          'book_id': bookId,
        }, onConflict: 'user_id,book_id');
        debugPrint('Bookmark added successfully');
      } else {
        // Remove bookmark
        await Supabase.instance.client
            .from('bookmarks')
            .delete()
            .eq('user_id', userId)
            .eq('book_id', bookId);
        debugPrint('Bookmark removed successfully');
      }
    } catch (e) {
      debugPrint('Error saving bookmark: $e');
      // Fallback to local storage
      final userId =
          Supabase.instance.client.auth.currentUser?.id ?? 'anonymous';
      final prefs = await SharedPreferences.getInstance();
      final localBookmarks = prefs.getStringList('bookmarks_$userId') ?? [];
      if (isBookmarked && !localBookmarks.contains(bookId)) {
        localBookmarks.add(bookId);
      } else if (!isBookmarked) {
        localBookmarks.remove(bookId);
      }
      await prefs.setStringList('bookmarks_$userId', localBookmarks);
      debugPrint('Saved bookmark to local storage as fallback');
    }
  }

  Future<bool> _checkUserExists(String userId) async {
    try {
      final existingUser = await Supabase.instance.client
          .from('users')
          .select('id')
          .eq('id', userId)
          .maybeSingle();

      return existingUser != null;
    } catch (e) {
      debugPrint('Error checking if user exists: $e');
      return false;
    }
  }

  Future<bool> _createUserProfile(String userId) async {
    try {
      final authUser = Supabase.instance.client.auth.currentUser;
      if (authUser == null) return false;

      // Create user profile with required fields
      await Supabase.instance.client.from('users').insert({
        'id': userId,
        'email': authUser.email ?? '<EMAIL>',
        'display_name':
            authUser.userMetadata?['full_name'] ??
            authUser.userMetadata?['name'] ??
            authUser.email?.split('@')[0] ??
            'Anonymous User',
      });

      debugPrint('Successfully created user profile for $userId');
      return true;
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      return false;
    }
  }

  void performSearch(String query) {
    if (query.isEmpty) {
      setState(() {
        searchResults = [];
      });
      return;
    }

    final results = <Map<String, dynamic>>[];

    for (int chapterIndex = 0; chapterIndex < chapters.length; chapterIndex++) {
      final chapter = chapters[chapterIndex];
      final content = _parseContentToText(chapter['content'] ?? '');
      final pages = _splitTextIntoPages(content);

      for (int pageIndex = 0; pageIndex < pages.length; pageIndex++) {
        final page = pages[pageIndex];
        final lowerPage = page.toLowerCase();
        final lowerQuery = query.toLowerCase();

        if (lowerPage.contains(lowerQuery)) {
          // Find the position of the match
          final matchIndex = lowerPage.indexOf(lowerQuery);
          final start = (matchIndex - 50).clamp(0, page.length);
          final end = (matchIndex + query.length + 50).clamp(0, page.length);
          final snippet = page.substring(start, end);

          results.add({
            'chapterIndex': chapterIndex,
            'pageIndex': pageIndex,
            'chapterTitle': chapter['title'] ?? 'Chapter ${chapterIndex + 1}',
            'snippet': snippet,
            'matchIndex': matchIndex,
          });
        }
      }
    }

    setState(() {
      searchResults = results;
    });
  }

  void navigateToSearchResult(int chapterIndex, int pageIndex) {
    setState(() {
      currentChapterIndex = chapterIndex;
      isLoading = true;
      showSearch = false;
    });

    _loadCurrentChapter().then((_) {
      setState(() {
        currentPageIndex = pageIndex;
      });
      pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  void toggleSearch() {
    setState(() {
      showSearch = !showSearch;
      if (!showSearch) {
        searchController.clear();
        searchResults = [];
      }
    });
  }

  void nextPage() {
    if (currentPageIndex < currentPages.length - 1) {
      setState(() {
        currentPageIndex++;
      });
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      nextChapter();
    }
    _calculateProgress();
  }

  void previousPage() {
    if (currentPageIndex > 0) {
      setState(() {
        currentPageIndex--;
      });
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      previousChapter();
    }
    _calculateProgress();
  }

  void nextChapter() {
    if (currentChapterIndex < chapters.length - 1) {
      setState(() {
        currentChapterIndex++;
        isLoading = true;
      });
      _loadCurrentChapter();
    }
  }

  void previousChapter() {
    if (currentChapterIndex > 0) {
      setState(() {
        currentChapterIndex--;
        isLoading = true;
      });
      _loadCurrentChapter().then((_) {
        // Go to last page of previous chapter
        setState(() {
          currentPageIndex = currentPages.length - 1;
        });
        pageController.animateToPage(
          currentPageIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  void toggleAppBar() {
    setState(() {
      showAppBar = !showAppBar;
    });
  }

  void toggleSettings() {
    setState(() {
      showSettings = !showSettings;
    });
    if (showSettings) {
      settingsAnimationController.forward();
    } else {
      settingsAnimationController.reverse();
    }
  }

  void toggleProgress() {
    setState(() {
      showProgress = !showProgress;
    });
    if (showProgress) {
      progressAnimationController.forward();
    } else {
      progressAnimationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(
        backgroundColor: isDarkMode ? Colors.black : Colors.white,
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      appBar: showAppBar ? buildAppBar() : null,
      body: Stack(
        children: [
          // Main reading area
          GestureDetector(
            onTap: toggleAppBar,
            child: PageView.builder(
              controller: pageController,
              onPageChanged: (index) {
                setState(() {
                  currentPageIndex = index;
                });
                _calculateProgress();
              },
              itemCount: currentPages.length,
              itemBuilder: (context, index) {
                return AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder: (child, animation) {
                    return FadeTransition(
                      opacity: animation,
                      child: SlideTransition(
                        position:
                            Tween<Offset>(
                              begin: const Offset(0.3, 0),
                              end: Offset.zero,
                            ).animate(
                              CurvedAnimation(
                                parent: animation,
                                curve: Curves.easeOutCubic,
                              ),
                            ),
                        child: child,
                      ),
                    );
                  },
                  child: buildPage(currentPages[index]),
                );
              },
            ),
          ),

          // Navigation areas (invisible tap zones)
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            width: MediaQuery.of(context).size.width * 0.3,
            child: GestureDetector(
              onTap: previousPage,
              child: Container(color: Colors.transparent),
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: MediaQuery.of(context).size.width * 0.3,
            child: GestureDetector(
              onTap: nextPage,
              child: Container(color: Colors.transparent),
            ),
          ),

          // Settings panel
          buildSettingsPanel(),

          // Progress panel
          buildProgressPanel(),

          // Search panel
          if (showSearch) buildSearchPanel(),

          // Reading progress indicator
          if (showAppBar)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 4,
                color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: readingProgress,
                  child: Container(color: Theme.of(context).primaryColor),
                ),
              ),
            ),
        ],
      ),
    );
  }

  PreferredSizeWidget buildAppBar() {
    return AppBar(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      foregroundColor: isDarkMode ? Colors.white : Colors.black,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.bookTitle,
            style: const TextStyle(fontSize: 16),
            overflow: TextOverflow.ellipsis,
          ),
          if (chapters.isNotEmpty)
            Text(
              chapters[currentChapterIndex]['title'] ??
                  'Chapter ${currentChapterIndex + 1}',
              style: TextStyle(
                fontSize: 12,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      actions: [
        IconButton(
          icon: Icon(
            bookmarks.contains(widget.bookId)
                ? Icons.bookmark
                : Icons.bookmark_border,
          ),
          onPressed: toggleBookmark,
        ),
        IconButton(icon: const Icon(Icons.list), onPressed: toggleProgress),
        IconButton(icon: const Icon(Icons.search), onPressed: toggleSearch),
        IconButton(
          icon: const Icon(Icons.picture_as_pdf),
          onPressed: () => PdfExportService.exportBookToPdf(
            bookTitle: widget.bookTitle,
            chapters: chapters,
            context: context,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.save),
          onPressed: () {
            saveReadingState();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Reading progress saved manually'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          tooltip: 'Save Reading Progress',
        ),
        IconButton(icon: const Icon(Icons.settings), onPressed: toggleSettings),
      ],
    );
  }

  Widget buildPage(String content) {
    return Container(
      padding: EdgeInsets.all(pageMargin),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                content,
                style: TextStyle(
                  fontSize: fontSize,
                  height: lineHeight,
                  color: isDarkMode ? Colors.white : Colors.black,
                  fontFamily: fontFamily == 'Default' ? null : fontFamily,
                ),
                textAlign: TextAlign.justify,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Page ${currentPageIndex + 1} of ${currentPages.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              Text(
                '${(readingProgress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildSettingsPanel() {
    return AnimatedBuilder(
      animation: settingsAnimationController,
      builder: (context, child) {
        return Positioned(
          right: -300 + (300 * settingsAnimationController.value),
          top: 0,
          bottom: 0,
          width: 300,
          child: Container(
            color: isDarkMode ? Colors.grey[900] : Colors.white,
            child: SafeArea(
              child: Column(
                children: [
                  AppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    title: const Text('Reading Settings'),
                    leading: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: toggleSettings,
                    ),
                  ),
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Font size
                        Text(
                          'Font Size',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Slider(
                          value: fontSize,
                          min: 12.0,
                          max: 24.0,
                          divisions: 12,
                          label: fontSize.round().toString(),
                          onChanged: (value) {
                            setState(() {
                              fontSize = value;
                            });
                            _loadCurrentChapter(); // Recalculate pages
                          },
                        ),
                        const SizedBox(height: 16),

                        // Line height
                        Text(
                          'Line Height',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Slider(
                          value: lineHeight,
                          min: 1.0,
                          max: 2.0,
                          divisions: 10,
                          label: lineHeight.toStringAsFixed(1),
                          onChanged: (value) {
                            setState(() {
                              lineHeight = value;
                            });
                            _loadCurrentChapter(); // Recalculate pages
                          },
                        ),
                        const SizedBox(height: 16),

                        // Dark mode
                        SwitchListTile(
                          title: const Text('Dark Mode'),
                          value: isDarkMode,
                          onChanged: (value) {
                            setState(() {
                              isDarkMode = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),

                        // Font family
                        Text(
                          'Font Family',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        DropdownButton<String>(
                          value: fontFamily,
                          isExpanded: true,
                          items: const [
                            DropdownMenuItem(
                              value: 'Default',
                              child: Text('Default'),
                            ),
                            DropdownMenuItem(
                              value: 'serif',
                              child: Text('Serif'),
                            ),
                            DropdownMenuItem(
                              value: 'monospace',
                              child: Text('Monospace'),
                            ),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                fontFamily = value;
                              });
                              _loadCurrentChapter(); // Recalculate pages
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildProgressPanel() {
    return AnimatedBuilder(
      animation: progressAnimationController,
      builder: (context, child) {
        return Positioned(
          left: 0,
          right: 0,
          bottom: -400 + (400 * progressAnimationController.value),
          height: 400,
          child: Container(
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[900] : Colors.white,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16),
                          child: Text(
                            'Chapters',
                            style: Theme.of(context).textTheme.titleLarge,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: toggleProgress,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: chapters.length,
                    itemBuilder: (context, index) {
                      final chapter = chapters[index];
                      final isCurrentChapter = index == currentChapterIndex;

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isCurrentChapter
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          child: Text('${index + 1}'),
                        ),
                        title: Text(
                          chapter['title'] ?? 'Chapter ${index + 1}',
                          style: TextStyle(
                            fontWeight: isCurrentChapter
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text('${chapter['word_count'] ?? 0} words'),
                        onTap: () {
                          setState(() {
                            currentChapterIndex = index;
                            isLoading = true;
                          });
                          _loadCurrentChapter();
                          toggleProgress();
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildSearchPanel() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      height: 300,
      child: Material(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        elevation: 8,
        child: SafeArea(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'Search in book...',
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        searchController.clear();
                        performSearch('');
                      },
                    ),
                  ),
                  onChanged: (value) {
                    performSearch(value);
                  },
                ),
              ),
              Expanded(
                child: searchResults.isEmpty
                    ? Center(
                        child: Text(
                          'No results',
                          style: TextStyle(
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: searchResults.length,
                        itemBuilder: (context, index) {
                          final result = searchResults[index];
                          return ListTile(
                            title: Text(result['chapterTitle'] ?? ''),
                            subtitle: Text(result['snippet'] ?? ''),
                            onTap: () {
                              navigateToSearchResult(
                                result['chapterIndex'] ?? 0,
                                result['pageIndex'] ?? 0,
                              );
                            },
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
