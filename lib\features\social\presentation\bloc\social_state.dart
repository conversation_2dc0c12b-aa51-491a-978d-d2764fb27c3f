import 'package:equatable/equatable.dart';
import '../../domain/entities/follow.dart';

import '../../domain/entities/comment.dart';
import '../../../auth/domain/entities/user.dart';

abstract class SocialState extends Equatable {
  const SocialState();

  @override
  List<Object?> get props => [];
}

class SocialInitial extends SocialState {}

class SocialLoading extends SocialState {}

class SocialError extends SocialState {
  final String message;

  const SocialError(this.message);

  @override
  List<Object> get props => [message];
}

// Follow States
class UserFollowed extends SocialState {
  final Follow follow;

  const UserFollowed(this.follow);

  @override
  List<Object> get props => [follow];
}

class UserUnfollowed extends SocialState {
  final String userId;

  const UserUnfollowed(this.userId);

  @override
  List<Object> get props => [userId];
}

class FollowStatusLoaded extends SocialState {
  final String userId;
  final bool isFollowing;

  const FollowStatusLoaded(this.userId, this.isFollowing);

  @override
  List<Object> get props => [userId, isFollowing];
}

class FollowersLoaded extends SocialState {
  final String userId;
  final List<User> followers;

  const FollowersLoaded(this.userId, this.followers);

  @override
  List<Object> get props => [userId, followers];
}

class FollowingLoaded extends SocialState {
  final String userId;
  final List<User> following;

  const FollowingLoaded(this.userId, this.following);

  @override
  List<Object> get props => [userId, following];
}

// Like States
class BookLiked extends SocialState {
  final String bookId;

  const BookLiked(this.bookId);

  @override
  List<Object> get props => [bookId];
}

class BookUnliked extends SocialState {
  final String bookId;

  const BookUnliked(this.bookId);

  @override
  List<Object> get props => [bookId];
}

class BookLikeStatusLoaded extends SocialState {
  final String bookId;
  final bool isLiked;

  const BookLikeStatusLoaded(this.bookId, this.isLiked);

  @override
  List<Object> get props => [bookId, isLiked];
}

// Comment States
class CommentAdded extends SocialState {
  final Comment comment;

  const CommentAdded(this.comment);

  @override
  List<Object> get props => [comment];
}

class CommentUpdated extends SocialState {
  final Comment comment;

  const CommentUpdated(this.comment);

  @override
  List<Object> get props => [comment];
}

class CommentDeleted extends SocialState {
  final String commentId;

  const CommentDeleted(this.commentId);

  @override
  List<Object> get props => [commentId];
}

class BookCommentsLoaded extends SocialState {
  final String bookId;
  final List<Comment> comments;

  const BookCommentsLoaded(this.bookId, this.comments);

  @override
  List<Object> get props => [bookId, comments];
}

class ChapterCommentsLoaded extends SocialState {
  final String chapterId;
  final List<Comment> comments;

  const ChapterCommentsLoaded(this.chapterId, this.comments);

  @override
  List<Object> get props => [chapterId, comments];
}
