import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/usecases/follow_user.dart';
import '../../domain/usecases/like_book.dart';
import '../../domain/usecases/add_comment.dart';
import 'social_event.dart';
import 'social_state.dart';

class SocialBloc extends Bloc<SocialEvent, SocialState> {
  final FollowUser followUser;
  final LikeBook likeBook;
  final AddComment addComment;

  SocialBloc({
    required this.followUser,
    required this.likeBook,
    required this.addComment,
  }) : super(SocialInitial()) {
    on<FollowUserRequested>(_onFollowUser);
    on<LikeBookRequested>(_onLikeBook);
    on<AddCommentRequested>(_onAddComment);
  }

  Future<void> _onFollowUser(
    FollowUserRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await followUser(FollowUserParams(userId: event.userId));

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (follow) => emit(UserFollowed(follow)),
    );
  }

  Future<void> _onLikeBook(
    LikeBookRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await likeBook(LikeBookParams(bookId: event.bookId));

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (like) => emit(BookLiked(event.bookId)),
    );
  }

  Future<void> _onAddComment(
    AddCommentRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await addComment(AddCommentParams(
      bookId: event.bookId,
      chapterId: event.chapterId,
      content: event.content,
      parentCommentId: event.parentCommentId,
    ));

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (comment) => emit(CommentAdded(comment)),
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Server error occurred';
      case CacheFailure _:
        return 'Cache error occurred';
      case NetworkFailure _:
        return 'Network error occurred';
      default:
        return 'Unexpected error occurred';
    }
  }
}
