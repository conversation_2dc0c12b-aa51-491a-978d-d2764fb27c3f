import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/usecases/follow_user.dart';
import '../../domain/usecases/like_book.dart';
import '../../domain/usecases/add_comment.dart';
import '../../domain/usecases/get_book_comments.dart';
import '../../domain/usecases/get_chapter_comments.dart';
import '../../domain/usecases/update_comment.dart';
import '../../domain/usecases/delete_comment.dart';
import 'social_event.dart';
import 'social_state.dart';

class SocialBloc extends Bloc<SocialEvent, SocialState> {
  final FollowUser followUser;
  final LikeBook likeBook;
  final AddComment addComment;
  final GetBookComments getBookComments;
  final GetChapterComments getChapterComments;
  final UpdateComment updateComment;
  final DeleteComment deleteComment;

  SocialBloc({
    required this.followUser,
    required this.likeBook,
    required this.addComment,
    required this.getBookComments,
    required this.getChapterComments,
    required this.updateComment,
    required this.deleteComment,
  }) : super(SocialInitial()) {
    on<FollowUserRequested>(_onFollowUser);
    on<LikeBookRequested>(_onLikeBook);
    on<AddCommentRequested>(_onAddComment);
    on<GetBookCommentsRequested>(_onGetBookComments);
    on<GetChapterCommentsRequested>(_onGetChapterComments);
    on<UpdateCommentRequested>(_onUpdateComment);
    on<DeleteCommentRequested>(_onDeleteComment);
  }

  Future<void> _onFollowUser(
    FollowUserRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await followUser(FollowUserParams(userId: event.userId));

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (follow) => emit(UserFollowed(follow)),
    );
  }

  Future<void> _onLikeBook(
    LikeBookRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await likeBook(LikeBookParams(bookId: event.bookId));

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (like) => emit(BookLiked(event.bookId)),
    );
  }

  Future<void> _onAddComment(
    AddCommentRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await addComment(
      AddCommentParams(
        bookId: event.bookId,
        chapterId: event.chapterId,
        content: event.content,
        parentCommentId: event.parentCommentId,
      ),
    );

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (comment) => emit(CommentAdded(comment)),
    );
  }

  Future<void> _onGetBookComments(
    GetBookCommentsRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await getBookComments(
      GetBookCommentsParams(bookId: event.bookId),
    );

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (comments) => emit(BookCommentsLoaded(event.bookId, comments)),
    );
  }

  Future<void> _onGetChapterComments(
    GetChapterCommentsRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await getChapterComments(
      GetChapterCommentsParams(chapterId: event.chapterId),
    );

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (comments) => emit(ChapterCommentsLoaded(event.chapterId, comments)),
    );
  }

  Future<void> _onUpdateComment(
    UpdateCommentRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await updateComment(
      UpdateCommentParams(commentId: event.commentId, content: event.content),
    );

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (comment) => emit(CommentUpdated(comment)),
    );
  }

  Future<void> _onDeleteComment(
    DeleteCommentRequested event,
    Emitter<SocialState> emit,
  ) async {
    emit(SocialLoading());

    final result = await deleteComment(
      DeleteCommentParams(commentId: event.commentId),
    );

    result.fold(
      (failure) => emit(SocialError(_mapFailureToMessage(failure))),
      (_) => emit(CommentDeleted(event.commentId)),
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Server error occurred';
      case CacheFailure _:
        return 'Cache error occurred';
      case NetworkFailure _:
        return 'Network error occurred';
      default:
        return 'Unexpected error occurred';
    }
  }
}
